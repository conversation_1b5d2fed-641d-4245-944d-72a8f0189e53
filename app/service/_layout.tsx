import { Stack } from 'expo-router';

export default function ServiceLayout() {
  return (
    <Stack
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="index" />
      <Stack.Screen name="client-selection" />
      <Stack.Screen name="new" />
      <Stack.Screen name="photo-guide" />
      <Stack.Screen name="safety-verification" />
      <Stack.Screen name="detail/[serviceId]" />
    </Stack>
  );
}
