import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { router } from 'expo-router';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { BeautyHeader } from '@/components/beauty';
import { BeautyButton } from '@/components/beauty';

export default function ServiceIndexScreen() {
  const handleNewService = () => {
    router.push('/service/client-selection');
  };

  const handleViewServices = () => {
    // Navigate to services list or dashboard
    router.push('/(tabs)');
  };

  return (
    <View style={styles.container}>
      <BeautyHeader
        title="Servicios"
        subtitle="Gestiona tus servicios de coloración"
        onBack={() => router.back()}
        showBackButton={true}
      />

      <View style={styles.content}>
        <Text style={styles.title}>Centro de Servicios</Text>
        <Text style={styles.description}>
          Crea nuevos servicios de coloración o gestiona los existentes
        </Text>

        <View style={styles.buttonContainer}>
          <BeautyButton
            title="Nuevo Servicio"
            onPress={handleNewService}
            variant="primary"
            style={styles.button}
          />

          <BeautyButton
            title="Ver Servicios"
            onPress={handleViewServices}
            variant="secondary"
            style={styles.button}
          />
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BeautyMinimalTheme.semantic.background.primary,
  },
  content: {
    flex: 1,
    padding: BeautyMinimalTheme.spacing.lg,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: BeautyMinimalTheme.typography.sizes.title,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
    textAlign: 'center',
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  description: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    fontWeight: BeautyMinimalTheme.typography.weights.regular,
    color: BeautyMinimalTheme.semantic.text.secondary,
    textAlign: 'center',
    marginBottom: BeautyMinimalTheme.spacing.xl,
    lineHeight: BeautyMinimalTheme.typography.sizes.body * BeautyMinimalTheme.typography.lineHeights.normal,
  },
  buttonContainer: {
    width: '100%',
    gap: BeautyMinimalTheme.spacing.md,
  },
  button: {
    width: '100%',
  },
});
