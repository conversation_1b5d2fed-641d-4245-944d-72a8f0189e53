import React, { useState, useEffect, useRef } from 'react';
import { StyleSheet, View, Alert } from 'react-native';
import { logger } from '@/utils/logger';
import { useLocalSearchParams, router } from 'expo-router';
import { useClientStore } from '@/stores/client-store';
import { useClientHistoryStore } from '@/stores/client-history-store';
import { useAIAnalysisStore } from '@/stores/ai-analysis-store';
import { useDashboardStore } from '@/stores/dashboard-store';
import { useSyncQueueStore } from '@/stores/sync-queue-store';
import Toast from '@/components/Toast';
import { BeautyHeader } from '@/components/beauty';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';

// Import our new components and hooks
import { useServiceFlow, STEPS } from '@/src/service/hooks/useServiceFlow';
import { useServicePersistence } from '@/src/service/hooks/useServicePersistence';
import { useServiceDraftStore } from '@/stores/service-draft-store';
import { StepIndicator } from '@/src/service/components/StepIndicator';
import { ServiceBreadcrumbs } from '@/src/service/components/ServiceBreadcrumbs';
import { DiagnosisStep } from '@/src/service/components/DiagnosisStep';
import { DesiredColorStep } from '@/src/service/components/DesiredColorStep';
import { FormulationStep } from '@/src/service/components/FormulationStep';
import { CompletionStep } from '@/src/service/components/CompletionStep';
import SmartTransitionDialog from '@/components/transitions/SmartTransitionDialog';

export default function NewServiceScreen() {
  const { clientId, restoreDraft } = useLocalSearchParams();
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [_lastSaved, _setLastSaved] = useState<Date | undefined>();
  const [isSaving, setIsSaving] = useState(false);
  const [_autoSaveTimer, _setAutoSaveTimer] = useState<number | null>(null);
  const navigationTimerRef = useRef<number | null>(null);
  const autoSaveIntervalRef = useRef<number | null>(null);
  const savingTimeoutRef = useRef<number | null>(null);
  const isSavingRef = useRef(false);
  const serviceDataRef = useRef(serviceData);
  const currentStepRef = useRef(currentStep);

  // Helper functions for breadcrumbs
  const getCompletedSteps = () => {
    const completed = [];
    for (let i = 0; i < currentStep; i++) {
      completed.push(i);
    }
    return completed;
  };

  const getStepSubtitle = (stepId: string): string | undefined => {
    switch (stepId) {
      case 'diagnosis':
        return serviceData.client?.name || 'Cliente no seleccionado';
      case 'desired':
        return serviceData.desiredAnalysisResult?.general?.overallLevel
          ? `Nivel ${serviceData.desiredAnalysisResult.general.overallLevel}`
          : undefined;
      case 'formulation':
        return serviceData.formula ? 'Fórmula generada' : undefined;
      case 'completion':
        return 'Finalizar servicio';
      default:
        return undefined;
    }
  };

  // Cleanup timers on unmount
  useEffect(() => {
    return () => {
      if (navigationTimerRef.current) {
        clearTimeout(navigationTimerRef.current);
      }
      if (_autoSaveTimer) {
        clearTimeout(_autoSaveTimer);
      }
      if (autoSaveIntervalRef.current) {
        clearInterval(autoSaveIntervalRef.current);
      }
    };
  }, [_autoSaveTimer]);

  // Use our new hooks
  const {
    currentStep,
    serviceData,
    updateServiceData,
    goToNextStep,
    goToPreviousStep,
    goToStep,
    canNavigateToStep,
    smartTransitions,
  } = useServiceFlow();

  const { saveServiceDraft, loadServiceDraft, deleteServiceDraft } = useServicePersistence();

  // Client Store
  const { getClient } = useClientStore();

  // Client History Store
  const { saveCompletedService } = useClientHistoryStore();

  // AI Analysis Store (for passing to components)
  const { analysisResult, clearAnalysis } = useAIAnalysisStore();

  // Dashboard Store (for refreshing metrics)
  const { refreshMetrics } = useDashboardStore();

  // Clear any previous AI analysis results when starting a new service
  useEffect(() => {
    clearAnalysis();
  }, [clearAnalysis]);

  // Load client data on mount
  useEffect(() => {
    const loadClientData = async () => {
      if (clientId && typeof clientId === 'string') {
        try {
          const clientData = await getClient(clientId);
          if (clientData) {
            updateServiceData({
              client: clientData,
              clientId: clientId,
            });

            // Try to load draft if requested
            if (restoreDraft === 'true') {
              const draft = loadServiceDraft(clientId);
              if (draft) {
                // Restore service data from draft - only restore compatible data
                const restoredData: Record<string, unknown> = {};

                if (draft.diagnosisData) {
                  Object.assign(restoredData, draft.diagnosisData);
                }
                if (draft.desiredData) {
                  Object.assign(restoredData, draft.desiredData);
                }
                if (draft.formulationData) {
                  Object.assign(restoredData, draft.formulationData);
                }
                if (draft.resultData) {
                  Object.assign(restoredData, draft.resultData);
                }

                updateServiceData(restoredData);
                showToastMessage('Borrador restaurado correctamente');
              }
            }
          }
        } catch (error) {
          logger.error('Error loading client', 'NewServiceScreen', error);
          showToastMessage('Error al cargar datos del cliente');
        }
      }
    };

    loadClientData();
  }, [clientId, restoreDraft, getClient, loadServiceDraft, updateServiceData]);

  // Clean up timers and intervals on unmount
  useEffect(() => {
    return () => {
      if (_autoSaveTimer) {
        clearTimeout(_autoSaveTimer);
      }
      if (autoSaveIntervalRef.current) {
        clearInterval(autoSaveIntervalRef.current);
        autoSaveIntervalRef.current = null;
      }
      if (savingTimeoutRef.current) {
        clearTimeout(savingTimeoutRef.current);
        savingTimeoutRef.current = null;
      }
    };
  }, [_autoSaveTimer]);

  // Sync state with refs to avoid dependency loops
  useEffect(() => {
    isSavingRef.current = isSaving;
    serviceDataRef.current = serviceData;
    currentStepRef.current = currentStep;
  }, [isSaving, serviceData, currentStep]);

  // Auto-save draft periodically - FIXED: No dependencies to prevent constant re-execution
  useEffect(() => {
    // Clear existing interval first
    if (autoSaveIntervalRef.current) {
      clearInterval(autoSaveIntervalRef.current);
      autoSaveIntervalRef.current = null;
    }

    // Clear any existing saving timeout
    if (savingTimeoutRef.current) {
      clearTimeout(savingTimeoutRef.current);
      savingTimeoutRef.current = null;
    }

    // Start auto-save interval (will check conditions inside the interval)
    autoSaveIntervalRef.current = setInterval(() => {
      // Get current state from refs to avoid stale closures
      const currentServiceData = serviceDataRef.current;
      const currentStepValue = currentStepRef.current;

      // Check ALL loading/processing states before auto-saving to avoid interference
      const aiStore = useAIAnalysisStore.getState();
      const syncStore = useSyncQueueStore.getState();
      const clientHistoryStore = useClientHistoryStore.getState();

      const isSystemBusy =
        // AI processing states
        aiStore.isAnalyzing ||
        aiStore.isAnalyzingDesiredPhoto ||
        // Sync and data states
        syncStore.isSyncing ||
        clientHistoryStore.isLoading ||
        // Local saving state - use ref to avoid dependency loop
        isSavingRef.current;

      // Only auto-save when system is truly idle and we have valid data
      if (!isSystemBusy && currentServiceData?.clientId && currentServiceData?.client) {
        setIsSaving(true);

        // Call saveServiceDraft directly using the store to avoid hook dependencies
        const { saveDraft, createDraftFromServiceState } = useServiceDraftStore.getState();

        const serviceState = {
          diagnosisMethod: currentServiceData.diagnosisMethod,
          hairPhotos: currentServiceData.hairPhotos,
          hairThickness: currentServiceData.hairThickness,
          hairDensity: currentServiceData.hairDensity,
          overallTone: currentServiceData.overallTone,
          overallUndertone: currentServiceData.overallUndertone,
          lastChemicalProcessType: currentServiceData.lastChemicalProcessType,
          lastChemicalProcessDate: currentServiceData.lastChemicalProcessDate,
          diagnosisNotes: currentServiceData.diagnosisNotes,
          zoneColorAnalysis: currentServiceData.zoneColorAnalysis,
          zonePhysicalAnalysis: currentServiceData.zonePhysicalAnalysis,
          desiredMethod: currentServiceData.desiredMethod,
          desiredPhotos: currentServiceData.desiredPhotos,
          desiredAnalysisResult: currentServiceData.desiredAnalysisResult,
          desiredNotes: currentServiceData.desiredNotes,
          selectedBrand: currentServiceData.selectedBrand,
          selectedLine: currentServiceData.selectedLine,
          formula: currentServiceData.formula,
          isFormulaFromAI: currentServiceData.isFormulaFromAI,
          formulaCost: currentServiceData.formulaCost,
          viabilityAnalysis: currentServiceData.viabilityAnalysis,
          stockValidation: currentServiceData.stockValidation,
          resultImage: currentServiceData.resultImage,
          clientSatisfaction: currentServiceData.clientSatisfaction,
          resultNotes: currentServiceData.resultNotes,
        };

        const draft = createDraftFromServiceState(
          currentServiceData.clientId,
          currentServiceData.client.name,
          currentStepValue,
          serviceState
        );

        saveDraft(draft);

        // Clear any existing saving timeout
        if (savingTimeoutRef.current) {
          clearTimeout(savingTimeoutRef.current);
          savingTimeoutRef.current = null;
        }

        // Set a robust timeout with safety mechanism
        savingTimeoutRef.current = setTimeout(() => {
          setIsSaving(false);
          _setLastSaved(new Date());
          savingTimeoutRef.current = null;
        }, 800);

        // Safety timeout to force reset if something goes wrong
        setTimeout(() => {
          if (isSavingRef.current) {
            setIsSaving(false);
            if (savingTimeoutRef.current) {
              clearTimeout(savingTimeoutRef.current);
              savingTimeoutRef.current = null;
            }
          }
        }, 2000);
      }
    }, 20000); // Auto-save every 20 seconds

    return () => {
      if (autoSaveIntervalRef.current) {
        clearInterval(autoSaveIntervalRef.current);
        autoSaveIntervalRef.current = null;
      }
      if (savingTimeoutRef.current) {
        clearTimeout(savingTimeoutRef.current);
        savingTimeoutRef.current = null;
      }
    };
  }, []); // No dependencies - interval runs independently

  const showToastMessage = (message: string) => {
    setToastMessage(message);
    setShowToast(true);
  };

  const performAutoSave = () => {
    if (serviceData.clientId) {
      setIsSaving(true);
      saveServiceDraft(serviceData, currentStep);

      // Show saving state for at least 800ms for better UX
      setTimeout(() => {
        setIsSaving(false);
        _setLastSaved(new Date());
      }, 800);
    }
  };

  const handleSaveDraft = (_silent = false) => {
    performAutoSave();
  };

  const handleFinishService = async () => {
    try {
      // DEBUG: Log the complete service data state before processing
      logger.info('🔍 DEBUGGING: Service completion started', 'NewServiceScreen', {
        clientName: serviceData.client?.name,
        hasClient: !!serviceData.client,
        hasFormula: !!serviceData.formula,
        hasFeedbackData: !!serviceData.feedbackData,
        feedbackRating: serviceData.feedbackData?.rating,
        clientSatisfaction: serviceData.clientSatisfaction,
        timestamp: new Date().toISOString(),
      });

      // Add service to client history
      if (serviceData.clientId && serviceData.client) {
        // Save the completed service to database and get the real service ID
        const savedService = await saveCompletedService({
          clientId: serviceData.clientId,
          clientName: serviceData.client.name,
          serviceType: 'color_service',
          formula: serviceData.formula,
          formulaData: serviceData.formulationData,
          technique: serviceData.applicationTechnique,
          processingTime: serviceData.processingTime,
          developerVolume: serviceData.developerVolume,
          satisfaction: serviceData.clientSatisfaction,
          notes: serviceData.resultNotes,
          beforePhotos: serviceData.diagnosisImage ? [serviceData.diagnosisImage] : [],
          afterPhotos: serviceData.resultImage ? [serviceData.resultImage] : [],
          aiAnalysis: analysisResult || null,
        });

        // DEBUG: Log the saved service details
        logger.info('💾 Service saved to database', 'NewServiceScreen', {
          serviceId: savedService?.id,
          clientName: serviceData.client.name,
        });

        // FIXED: Now save contextual feedback with the real service ID
        if (serviceData.feedbackData && serviceData.formula && savedService?.id) {
          try {
            const { useFormulaFeedbackStore } = await import('@/stores/formula-feedback-store');
            const { useAuthStore } = await import('@/stores/auth-store');
            const { useSalonConfigStore } = await import('@/stores/salon-config-store');

            const currentUser = useAuthStore.getState().user;
            const currentSalonId = useSalonConfigStore.getState().salonId;

            if (currentUser && currentSalonId) {
              const feedbackData = {
                formula_id:
                  serviceData.formulaId || `formula_${serviceData.clientId}_${Date.now()}`,
                salon_id: currentSalonId,
                user_id: currentUser.id,
                service_id: savedService.id, // Use the REAL service ID from the database

                // Core feedback from the stored data
                worked_as_expected: serviceData.feedbackData.worked_as_expected,
                rating: serviceData.feedbackData.rating,
                would_use_again: serviceData.feedbackData.would_use_again,

                // Enhanced contextual details
                actual_result: serviceData.feedbackData.actual_result,
                adjustments_made: serviceData.feedbackData.adjustments_made,
                hair_type: serviceData.feedbackData.hair_type,
                environmental_factors: serviceData.feedbackData.environmental_factors,
              };

              logger.info(
                'Saving contextual feedback immediately after service creation',
                'NewServiceScreen',
                {
                  rating: feedbackData.rating,
                  workedAsExpected: feedbackData.worked_as_expected,
                  realServiceId: savedService.id,
                  clientName: serviceData.client.name,
                }
              );

              await useFormulaFeedbackStore.getState().addFeedback(feedbackData, true); // isCritical = true

              logger.info('Contextual feedback successfully saved', 'NewServiceScreen', {
                rating: feedbackData.rating,
                realServiceId: savedService.id,
                clientName: serviceData.client.name,
              });
            } else {
              logger.warn('Missing user or salon data for feedback', 'NewServiceScreen', {
                hasUser: !!currentUser,
                hasSalonId: !!currentSalonId,
              });
            }
          } catch (feedbackError) {
            logger.error('Failed to save feedback with real service ID', 'NewServiceScreen', {
              error: feedbackError.message,
              serviceId: savedService.id,
            });
            // Don't throw - feedback is not critical for service completion
          }
        } else {
          logger.warn('❌ FEEDBACK NOT SAVED - Missing required data', 'NewServiceScreen', {
            hasFeedbackData: !!serviceData.feedbackData,
            hasFormula: !!serviceData.formula,
            hasSavedService: !!savedService?.id,
            // DEBUG: Show actual feedback data structure
            feedbackDataContent: serviceData.feedbackData
              ? {
                  rating: serviceData.feedbackData.rating,
                  worked_as_expected: serviceData.feedbackData.worked_as_expected,
                  actual_result: serviceData.feedbackData.actual_result,
                }
              : null,
            formulaLength: serviceData.formula ? serviceData.formula.length : 0,
            savedServiceId: savedService?.id,
            clientName: serviceData.client?.name,
            timestamp: new Date().toISOString(),
          });
        }

        // Delete draft after successful completion
        deleteServiceDraft(serviceData.clientId);

        // Stop auto-save timer since service is completed
        if (autoSaveIntervalRef.current) {
          clearInterval(autoSaveIntervalRef.current);
          autoSaveIntervalRef.current = null;
        }

        // Small delay to ensure Zustand persists the deletion to AsyncStorage
        await new Promise(resolve => setTimeout(resolve, 100));

        // Refresh dashboard metrics
        await refreshMetrics();

        showToastMessage('Servicio completado y guardado');

        // Navigate to dashboard after a short delay
        navigationTimerRef.current = setTimeout(() => {
          router.replace('/(tabs)');
        }, 1500);
      }
    } catch (error) {
      logger.error('Error finishing service', 'NewServiceScreen', error);
      Alert.alert('Error', 'No se pudo completar el servicio. Por favor intenta nuevamente.');
    }
  };

  const handleStepNavigation = (stepIndex: number) => {
    if (canNavigateToStep(stepIndex)) {
      goToStep(stepIndex);
    } else {
      Alert.alert('Paso no disponible', 'Completa los pasos anteriores antes de continuar.', [
        { text: 'OK' },
      ]);
    }
  };

  // Silent save for AI analysis results
  const handleSaveDraftSilent = () => {
    handleSaveDraft(true);
  };

  // Render the appropriate step component
  const renderCurrentStep = () => {
    const commonProps = {
      data: serviceData,
      onUpdate: (updates: Record<string, unknown>) => {
        updateServiceData(updates);

        // DISABLED: Auto-save timer to prevent conflicts with interval-based auto-save
        // The interval-based auto-save (every 20 seconds) handles all automatic saving
        // Manual saves are still available through onSave and onSaveSilent
      },
      onNext: goToNextStep,
      onBack: goToPreviousStep,
      onSave: handleSaveDraft,
      onSaveSilent: handleSaveDraftSilent,
    };

    switch (currentStep) {
      case 0:
        return <DiagnosisStep {...commonProps} />;
      case 1:
        return <DesiredColorStep {...commonProps} />;
      case 2:
        return <FormulationStep {...commonProps} analysisResult={analysisResult} />;
      case 3:
        return <CompletionStep {...commonProps} onNext={handleFinishService} />;
      default:
        return <DiagnosisStep {...commonProps} />;
    }
  };

  return (
    <View style={styles.container}>
      <BeautyHeader
        title={STEPS[currentStep]?.title || 'Servicio'}
        subtitle={serviceData.client?.name}
        onBack={goToPreviousStep}
        showBackButton={true}
      />

      <ServiceBreadcrumbs
        steps={STEPS.map(step => ({
          ...step,
          subtitle: getStepSubtitle(step.id),
        }))}
        currentStep={currentStep}
        completedSteps={getCompletedSteps()}
        onStepPress={handleStepNavigation}
        canNavigate={(stepIndex: number) => Boolean(canNavigateToStep(stepIndex))}
      />

      <StepIndicator
        steps={STEPS}
        currentStep={currentStep}
        onStepPress={handleStepNavigation}
        canNavigateToStep={(stepIndex: number) => Boolean(canNavigateToStep(stepIndex))}
      />

      {renderCurrentStep()}

      {/* Smart Transitions Dialog */}
      <SmartTransitionDialog
        visible={smartTransitions.isDialogVisible}
        validation={smartTransitions.currentValidation}
        onProceed={() => {
          const targetStep = smartTransitions.proceedWithTransition();
          if (targetStep !== null) {
            goToStep(targetStep);
          }
        }}
        onCancel={smartTransitions.cancelTransition}
        onRecommendationPress={smartTransitions.handleRecommendation}
      />

      {showToast && <Toast message={toastMessage} onHide={() => setShowToast(false)} />}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BeautyMinimalTheme.semantic.background.primary, // 90% neutral foundation
  },
});

export default NewServiceScreen;
