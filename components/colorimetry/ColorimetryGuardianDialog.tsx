/**
 * Colorimetry Guardian Dialog
 * Converts technical violations into conversational guidance
 * Replaces error messages with professional, helpful dialogs
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Modal,
  Alert,
} from 'react-native';
import { 
  AlertTriangle, 
  Shield, 
  CheckCircle, 
  Clock, 
  ArrowRight,
  X,
  Lightbulb
} from 'lucide-react-native';
import * as Haptics from 'expo-haptics';
import { BeautyCard, BeautyButton } from '@/components/beauty';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { getTypographyStyle } from '@/constants/typography-system';
import Colors from '@/constants/colors';

// Import the new types from formula-validator
import type { 
  ColorimetryViolation, 
  PreValidationResult 
} from '@/supabase/functions/salonier-assistant/utils/formula-validator';

interface ColorimetryGuardianDialogProps {
  visible: boolean;
  validationResult: PreValidationResult;
  onProceed: () => void;
  onCancel: () => void;
  onSelectAlternative: (approach: string) => void;
}

export const ColorimetryGuardianDialog: React.FC<ColorimetryGuardianDialogProps> = ({
  visible,
  validationResult,
  onProceed,
  onCancel,
  onSelectAlternative,
}) => {
  const [selectedAlternative, setSelectedAlternative] = useState<string | null>(null);

  const getViolationIcon = (type: 'critical' | 'warning' | 'info') => {
    switch (type) {
      case 'critical':
        return <AlertTriangle size={24} color={Colors.light.error} />;
      case 'warning':
        return <Shield size={24} color={Colors.light.warning} />;
      case 'info':
        return <Lightbulb size={24} color={Colors.light.info} />;
    }
  };

  const getViolationColor = (type: 'critical' | 'warning' | 'info') => {
    switch (type) {
      case 'critical':
        return Colors.light.error;
      case 'warning':
        return Colors.light.warning;
      case 'info':
        return Colors.light.info;
    }
  };

  const handleProceedWithConfirmation = () => {
    const criticalViolations = validationResult.violations.filter(v => v.type === 'critical');
    
    if (criticalViolations.length > 0) {
      Alert.alert(
        'Confirmación Requerida',
        'Has decidido proceder a pesar de las advertencias críticas. ¿Estás seguro de que quieres continuar?',
        [
          { text: 'Cancelar', style: 'cancel' },
          { 
            text: 'Sí, continuar', 
            style: 'destructive',
            onPress: () => {
              Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
              onProceed();
            }
          }
        ]
      );
    } else {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      onProceed();
    }
  };

  const renderViolation = (violation: ColorimetryViolation, index: number) => (
    <BeautyCard key={index} variant="default" style={styles.violationCard}>
      <View style={styles.violationHeader}>
        {getViolationIcon(violation.type)}
        <View style={styles.violationTitleContainer}>
          <Text style={[styles.violationTitle, { color: getViolationColor(violation.type) }]}>
            {violation.type === 'critical' ? 'Atención Crítica' : 
             violation.type === 'warning' ? 'Precaución Profesional' : 'Recomendación'}
          </Text>
        </View>
      </View>
      
      <Text style={styles.conversationalMessage}>
        {violation.conversationalMessage}
      </Text>
      
      <View style={styles.suggestedActionContainer}>
        <Text style={styles.suggestedActionLabel}>Acción recomendada:</Text>
        <Text style={styles.suggestedAction}>{violation.suggestedAction}</Text>
      </View>

      {violation.alternativeApproaches && violation.alternativeApproaches.length > 0 && (
        <View style={styles.alternativesContainer}>
          <Text style={styles.alternativesLabel}>Alternativas disponibles:</Text>
          {violation.alternativeApproaches.map((alternative, altIndex) => (
            <TouchableOpacity
              key={altIndex}
              style={[
                styles.alternativeOption,
                selectedAlternative === alternative && styles.alternativeOptionSelected
              ]}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                setSelectedAlternative(alternative);
              }}
            >
              <Text style={[
                styles.alternativeText,
                selectedAlternative === alternative && styles.alternativeTextSelected
              ]}>
                {alternative}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      )}
    </BeautyCard>
  );

  const renderApproachSummary = () => {
    const { recommendedApproach, estimatedSessions, riskLevel } = validationResult;
    
    let approachIcon;
    let approachColor;
    let approachTitle;
    
    switch (recommendedApproach) {
      case 'direct':
        approachIcon = <CheckCircle size={20} color={Colors.light.success} />;
        approachColor = Colors.light.success;
        approachTitle = 'Proceso Directo';
        break;
      case 'gradual':
        approachIcon = <Clock size={20} color={Colors.light.warning} />;
        approachColor = Colors.light.warning;
        approachTitle = 'Proceso Gradual';
        break;
      case 'alternative':
        approachIcon = <ArrowRight size={20} color={Colors.light.info} />;
        approachColor = Colors.light.info;
        approachTitle = 'Enfoque Alternativo';
        break;
      case 'abort':
        approachIcon = <X size={20} color={Colors.light.error} />;
        approachColor = Colors.light.error;
        approachTitle = 'No Recomendado';
        break;
    }

    return (
      <BeautyCard variant="elevated" style={[styles.summaryCard, { borderLeftColor: approachColor }]}>
        <View style={styles.summaryHeader}>
          {approachIcon}
          <Text style={[styles.summaryTitle, { color: approachColor }]}>{approachTitle}</Text>
        </View>
        
        <Text style={styles.conversationalSummary}>
          {validationResult.conversationalSummary}
        </Text>
        
        <View style={styles.summaryDetails}>
          <Text style={styles.summaryDetail}>
            Sesiones estimadas: <Text style={styles.summaryDetailValue}>{estimatedSessions}</Text>
          </Text>
          <Text style={styles.summaryDetail}>
            Nivel de riesgo: <Text style={[styles.summaryDetailValue, { color: approachColor }]}>
              {riskLevel.charAt(0).toUpperCase() + riskLevel.slice(1)}
            </Text>
          </Text>
        </View>
      </BeautyCard>
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onCancel}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Guardián de Colorimetría</Text>
          <TouchableOpacity onPress={onCancel} style={styles.closeButton}>
            <X size={24} color={BeautyMinimalTheme.semantic.text.secondary} />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {renderApproachSummary()}
          
          {validationResult.violations.map((violation, index) => 
            renderViolation(violation, index)
          )}
        </ScrollView>

        <View style={styles.footer}>
          {validationResult.canProceed ? (
            <>
              <BeautyButton
                variant="secondary"
                onPress={onCancel}
                style={styles.footerButton}
              >
                Revisar Diagnóstico
              </BeautyButton>
              
              {selectedAlternative ? (
                <BeautyButton
                  variant="primary"
                  onPress={() => onSelectAlternative(selectedAlternative)}
                  style={styles.footerButton}
                >
                  Usar Alternativa
                </BeautyButton>
              ) : (
                <BeautyButton
                  variant="primary"
                  onPress={handleProceedWithConfirmation}
                  style={styles.footerButton}
                >
                  Continuar con Precauciones
                </BeautyButton>
              )}
            </>
          ) : (
            <>
              <BeautyButton
                variant="primary"
                onPress={onCancel}
                style={[styles.footerButton, { flex: 1 }]}
              >
                Revisar y Ajustar
              </BeautyButton>
            </>
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BeautyMinimalTheme.semantic.background.primary,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: BeautyMinimalTheme.semantic.border.subtle,
  },
  headerTitle: {
    ...getTypographyStyle('heading', 'large'),
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  closeButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  summaryCard: {
    marginVertical: 16,
    borderLeftWidth: 4,
  },
  summaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  summaryTitle: {
    ...getTypographyStyle('heading', 'medium'),
    marginLeft: 8,
  },
  conversationalSummary: {
    ...getTypographyStyle('body', 'large'),
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: 16,
    lineHeight: 24,
  },
  summaryDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  summaryDetail: {
    ...getTypographyStyle('body', 'medium'),
    color: BeautyMinimalTheme.semantic.text.secondary,
  },
  summaryDetailValue: {
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  violationCard: {
    marginVertical: 8,
  },
  violationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  violationTitleContainer: {
    marginLeft: 12,
  },
  violationTitle: {
    ...getTypographyStyle('heading', 'small'),
    fontWeight: '600',
  },
  conversationalMessage: {
    ...getTypographyStyle('body', 'large'),
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: 16,
    lineHeight: 22,
  },
  suggestedActionContainer: {
    backgroundColor: BeautyMinimalTheme.semantic.background.secondary,
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  suggestedActionLabel: {
    ...getTypographyStyle('body', 'small'),
    color: BeautyMinimalTheme.semantic.text.secondary,
    fontWeight: '600',
    marginBottom: 4,
  },
  suggestedAction: {
    ...getTypographyStyle('body', 'medium'),
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  alternativesContainer: {
    marginTop: 8,
  },
  alternativesLabel: {
    ...getTypographyStyle('body', 'small'),
    color: BeautyMinimalTheme.semantic.text.secondary,
    fontWeight: '600',
    marginBottom: 8,
  },
  alternativeOption: {
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.semantic.border.subtle,
    marginBottom: 8,
  },
  alternativeOptionSelected: {
    borderColor: BeautyMinimalTheme.semantic.interactive.primary.default,
    backgroundColor: BeautyMinimalTheme.semantic.interactive.primary.default + '10',
  },
  alternativeText: {
    ...getTypographyStyle('body', 'medium'),
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  alternativeTextSelected: {
    color: BeautyMinimalTheme.semantic.interactive.primary.default,
    fontWeight: '600',
  },
  footer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 20,
    paddingBottom: 40,
    borderTopWidth: 1,
    borderTopColor: BeautyMinimalTheme.semantic.border.subtle,
    gap: 12,
  },
  footerButton: {
    flex: 1,
  },
});
