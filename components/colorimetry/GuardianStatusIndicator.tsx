/**
 * Guardian Status Indicator
 * Shows the current status of Colorimetry Guardian validation
 * Provides quick visual feedback about process safety
 */

import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Shield, ShieldCheck, ShieldAlert, ShieldX, Info } from 'lucide-react-native';
import * as Haptics from 'expo-haptics';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { getTypographyStyle } from '@/constants/typography-system';
import Colors from '@/constants/colors';

import type { PreValidationResult } from '@/supabase/functions/salonier-assistant/utils/formula-validator';

interface GuardianStatusIndicatorProps {
  validationResult: PreValidationResult | null;
  isValidating?: boolean;
  onPress?: () => void;
  compact?: boolean;
}

export const GuardianStatusIndicator: React.FC<GuardianStatusIndicatorProps> = ({
  validationResult,
  isValidating = false,
  onPress,
  compact = false,
}) => {
  
  const getStatusConfig = () => {
    if (isValidating) {
      return {
        icon: Shield,
        color: BeautyMinimalTheme.semantic.text.secondary,
        backgroundColor: BeautyMinimalTheme.semantic.background.secondary,
        title: 'Validando...',
        subtitle: 'Verificando seguridad del proceso',
        borderColor: BeautyMinimalTheme.semantic.border.subtle,
      };
    }

    if (!validationResult) {
      return {
        icon: Shield,
        color: BeautyMinimalTheme.semantic.text.tertiary,
        backgroundColor: BeautyMinimalTheme.semantic.background.secondary,
        title: 'Sin validar',
        subtitle: 'Pendiente de análisis de seguridad',
        borderColor: BeautyMinimalTheme.semantic.border.subtle,
      };
    }

    const { violations, riskLevel, canProceed } = validationResult;
    const criticalCount = violations.filter(v => v.type === 'critical').length;

    if (criticalCount > 0) {
      return {
        icon: ShieldX,
        color: Colors.light.error,
        backgroundColor: Colors.light.error + '10',
        title: 'Riesgo Crítico',
        subtitle: `${criticalCount} violación${criticalCount > 1 ? 'es' : ''} crítica${criticalCount > 1 ? 's' : ''}`,
        borderColor: Colors.light.error,
      };
    }

    if (violations.length > 0) {
      return {
        icon: ShieldAlert,
        color: Colors.light.warning,
        backgroundColor: Colors.light.warning + '10',
        title: riskLevel === 'medium' ? 'Precaución' : 'Advertencias',
        subtitle: `${violations.length} consideración${violations.length > 1 ? 'es' : ''} importante${violations.length > 1 ? 's' : ''}`,
        borderColor: Colors.light.warning,
      };
    }

    return {
      icon: ShieldCheck,
      color: Colors.light.success,
      backgroundColor: Colors.light.success + '10',
      title: 'Proceso Seguro',
      subtitle: 'Validación exitosa - sin riesgos detectados',
      borderColor: Colors.light.success,
    };
  };

  const config = getStatusConfig();
  const IconComponent = config.icon;

  const handlePress = () => {
    if (onPress) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      onPress();
    }
  };

  if (compact) {
    return (
      <TouchableOpacity
        style={[
          styles.compactContainer,
          { 
            backgroundColor: config.backgroundColor,
            borderColor: config.borderColor,
          }
        ]}
        onPress={handlePress}
        disabled={!onPress}
        activeOpacity={onPress ? 0.7 : 1}
      >
        <IconComponent size={16} color={config.color} />
        <Text style={[styles.compactTitle, { color: config.color }]}>
          {config.title}
        </Text>
        {validationResult?.violations.length > 0 && (
          <View style={[styles.badge, { backgroundColor: config.color }]}>
            <Text style={styles.badgeText}>{validationResult.violations.length}</Text>
          </View>
        )}
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity
      style={[
        styles.container,
        { 
          backgroundColor: config.backgroundColor,
          borderColor: config.borderColor,
        }
      ]}
      onPress={handlePress}
      disabled={!onPress}
      activeOpacity={onPress ? 0.7 : 1}
    >
      <View style={styles.header}>
        <View style={styles.iconContainer}>
          <IconComponent size={24} color={config.color} />
        </View>
        <View style={styles.textContainer}>
          <Text style={[styles.title, { color: config.color }]}>
            {config.title}
          </Text>
          <Text style={styles.subtitle}>
            {config.subtitle}
          </Text>
        </View>
        {validationResult?.violations.length > 0 && (
          <View style={[styles.violationBadge, { backgroundColor: config.color }]}>
            <Text style={styles.violationBadgeText}>
              {validationResult.violations.length}
            </Text>
          </View>
        )}
      </View>

      {validationResult && validationResult.violations.length > 0 && onPress && (
        <View style={styles.footer}>
          <Info size={14} color={BeautyMinimalTheme.semantic.text.tertiary} />
          <Text style={styles.footerText}>
            Toca para ver detalles y recomendaciones
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    borderWidth: 1,
    borderRadius: BeautyMinimalTheme.radius.md,
    padding: BeautyMinimalTheme.spacing.md,
    marginVertical: BeautyMinimalTheme.spacing.xs,
  },
  compactContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: BeautyMinimalTheme.radius.sm,
    paddingHorizontal: BeautyMinimalTheme.spacing.sm,
    paddingVertical: BeautyMinimalTheme.spacing.xs,
    gap: BeautyMinimalTheme.spacing.xs,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    marginRight: BeautyMinimalTheme.spacing.sm,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    ...getTypographyStyle('heading', 'small'),
    fontWeight: '600',
    marginBottom: 2,
  },
  compactTitle: {
    ...getTypographyStyle('body', 'small'),
    fontWeight: '600',
  },
  subtitle: {
    ...getTypographyStyle('body', 'small'),
    color: BeautyMinimalTheme.semantic.text.secondary,
  },
  violationBadge: {
    minWidth: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: BeautyMinimalTheme.spacing.sm,
  },
  violationBadgeText: {
    ...getTypographyStyle('body', 'small'),
    color: 'white',
    fontWeight: '600',
    fontSize: 12,
  },
  badge: {
    minWidth: 18,
    height: 18,
    borderRadius: 9,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeText: {
    ...getTypographyStyle('body', 'small'),
    color: 'white',
    fontWeight: '600',
    fontSize: 10,
  },
  footer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: BeautyMinimalTheme.spacing.sm,
    paddingTop: BeautyMinimalTheme.spacing.sm,
    borderTopWidth: 1,
    borderTopColor: BeautyMinimalTheme.semantic.border.subtle,
    gap: BeautyMinimalTheme.spacing.xs,
  },
  footerText: {
    ...getTypographyStyle('body', 'small'),
    color: BeautyMinimalTheme.semantic.text.tertiary,
    fontStyle: 'italic',
  },
});
