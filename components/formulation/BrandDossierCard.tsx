import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { BeautyCard } from '@/components/beauty/BeautyCard';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import BrandQuickInfo from './BrandQuickInfo';

interface BrandDossier {
  numberingSystem: string;
  maxDeveloperVolume: number;
  specialFeatures: string[];
  compatibleBrands: string[];
  professionalNotes: string;
  commonIssues: string[];
  expertTips: string[];
  regionalNotes?: string;
}

interface LineDossier {
  type: 'permanent' | 'demi-permanent' | 'semi-permanent' | 'temporary' | 'bleaching';
  mixingRatio: string;
  processingTime: string;
  pigmentBase: 'warm' | 'cool' | 'neutral' | 'variable';
  coverageLevel: 'light' | 'medium' | 'full' | 'maximum';
  liftingPower: number;
  specialistNotes: string;
  bestFor: string[];
  limitations: string[];
  neutralizationTips: string;
  mixingNotes: string;
  applicationTips: string;
  durability: 'low' | 'medium' | 'high' | 'excellent';
  fadePattern: string;
  maintenanceNotes: string;
}

interface BrandDossierCardProps {
  brandName: string;
  lineName: string;
  brandDossier?: BrandDossier;
  lineDossier?: LineDossier;
  isExpanded?: boolean;
  onToggle?: () => void;
}

export const BrandDossierCard: React.FC<BrandDossierCardProps> = ({
  brandName,
  lineName,
  brandDossier,
  lineDossier,
  isExpanded = false,
  onToggle,
}) => {
  const [activeTab, setActiveTab] = useState<'brand' | 'line'>('brand');

  if (!brandDossier && !lineDossier) {
    return null;
  }

  const renderBrandInfo = () => (
    <View style={styles.contentSection}>
      <View style={styles.infoGrid}>
        <View style={styles.infoItem}>
          <Text style={styles.infoLabel}>Sistema de Numeración</Text>
          <Text style={styles.infoValue}>{brandDossier?.numberingSystem}</Text>
        </View>
        <View style={styles.infoItem}>
          <Text style={styles.infoLabel}>Oxidante Máximo</Text>
          <Text style={styles.infoValue}>{brandDossier?.maxDeveloperVolume}vol</Text>
        </View>
      </View>

      {brandDossier?.specialFeatures && brandDossier.specialFeatures.length > 0 && (
        <View style={styles.featuresSection}>
          <Text style={styles.sectionTitle}>Características Especiales</Text>
          <View style={styles.tagContainer}>
            {brandDossier.specialFeatures.map((feature, index) => (
              <View key={index} style={styles.featureTag}>
                <Text style={styles.featureText}>{feature}</Text>
              </View>
            ))}
          </View>
        </View>
      )}

      {brandDossier?.professionalNotes && (
        <View style={styles.notesSection}>
          <Text style={styles.sectionTitle}>Notas Profesionales</Text>
          <Text style={styles.notesText}>{brandDossier.professionalNotes}</Text>
        </View>
      )}

      {brandDossier?.expertTips && brandDossier.expertTips.length > 0 && (
        <View style={styles.tipsSection}>
          <Text style={styles.sectionTitle}>Tips de Experto</Text>
          {brandDossier.expertTips.map((tip, index) => (
            <View key={index} style={styles.tipItem}>
              <Ionicons name="bulb" size={14} color={BeautyMinimalTheme.semantic.accent.primary} />
              <Text style={styles.tipText}>{tip}</Text>
            </View>
          ))}
        </View>
      )}
    </View>
  );

  const renderLineInfo = () => (
    <View style={styles.contentSection}>
      <View style={styles.infoGrid}>
        <View style={styles.infoItem}>
          <Text style={styles.infoLabel}>Tipo</Text>
          <Text style={styles.infoValue}>{lineDossier?.type}</Text>
        </View>
        <View style={styles.infoItem}>
          <Text style={styles.infoLabel}>Mezcla</Text>
          <Text style={styles.infoValue}>{lineDossier?.mixingRatio}</Text>
        </View>
        <View style={styles.infoItem}>
          <Text style={styles.infoLabel}>Tiempo</Text>
          <Text style={styles.infoValue}>{lineDossier?.processingTime}</Text>
        </View>
        <View style={styles.infoItem}>
          <Text style={styles.infoLabel}>Cobertura</Text>
          <Text style={styles.infoValue}>{lineDossier?.coverageLevel}</Text>
        </View>
      </View>

      {lineDossier?.bestFor && lineDossier.bestFor.length > 0 && (
        <View style={styles.featuresSection}>
          <Text style={styles.sectionTitle}>Ideal Para</Text>
          <View style={styles.tagContainer}>
            {lineDossier.bestFor.map((use, index) => (
              <View key={index} style={[styles.featureTag, styles.bestForTag]}>
                <Text style={styles.featureText}>{use}</Text>
              </View>
            ))}
          </View>
        </View>
      )}

      {lineDossier?.specialistNotes && (
        <View style={styles.notesSection}>
          <Text style={styles.sectionTitle}>Notas del Especialista</Text>
          <Text style={styles.notesText}>{lineDossier.specialistNotes}</Text>
        </View>
      )}

      {lineDossier?.neutralizationTips && (
        <View style={styles.tipsSection}>
          <Text style={styles.sectionTitle}>Consejos de Neutralización</Text>
          <View style={styles.tipItem}>
            <Ionicons name="color-palette" size={14} color={BeautyMinimalTheme.semantic.accent.primary} />
            <Text style={styles.tipText}>{lineDossier.neutralizationTips}</Text>
          </View>
        </View>
      )}

      {lineDossier?.applicationTips && (
        <View style={styles.tipsSection}>
          <Text style={styles.sectionTitle}>Consejos de Aplicación</Text>
          <View style={styles.tipItem}>
            <Ionicons name="brush" size={14} color={BeautyMinimalTheme.semantic.accent.primary} />
            <Text style={styles.tipText}>{lineDossier.applicationTips}</Text>
          </View>
        </View>
      )}
    </View>
  );

  return (
    <BeautyCard variant="default" style={styles.container}>
      <TouchableOpacity style={styles.header} onPress={onToggle}>
        <View style={styles.headerContent}>
          <View style={styles.brandInfo}>
            <Text style={styles.brandName}>{brandName}</Text>
            <Text style={styles.lineName}>{lineName}</Text>
          </View>
          <View style={styles.expertBadge}>
            <Ionicons name="school" size={16} color={BeautyMinimalTheme.semantic.accent.primary} />
            <Text style={styles.expertText}>Experto</Text>
          </View>
        </View>
        <Ionicons
          name={isExpanded ? "chevron-up" : "chevron-down"}
          size={20}
          color={BeautyMinimalTheme.semantic.text.secondary}
        />
      </TouchableOpacity>

      {/* Quick Info when collapsed */}
      {!isExpanded && (brandDossier || lineDossier) && (
        <BrandQuickInfo
          mixingRatio={lineDossier?.mixingRatio}
          processingTime={lineDossier?.processingTime}
          maxDeveloperVolume={brandDossier?.maxDeveloperVolume}
          coverageLevel={lineDossier?.coverageLevel}
          liftingPower={lineDossier?.liftingPower}
          type={lineDossier?.type}
        />
      )}

      {isExpanded && (
        <View style={styles.expandedContent}>
          {brandDossier && lineDossier && (
            <View style={styles.tabContainer}>
              <TouchableOpacity
                style={[styles.tab, activeTab === 'brand' && styles.activeTab]}
                onPress={() => setActiveTab('brand')}
              >
                <Text style={[styles.tabText, activeTab === 'brand' && styles.activeTabText]}>
                  Marca
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.tab, activeTab === 'line' && styles.activeTab]}
                onPress={() => setActiveTab('line')}
              >
                <Text style={[styles.tabText, activeTab === 'line' && styles.activeTabText]}>
                  Línea
                </Text>
              </TouchableOpacity>
            </View>
          )}

          {activeTab === 'brand' && brandDossier && renderBrandInfo()}
          {activeTab === 'line' && lineDossier && renderLineInfo()}
          {!brandDossier && lineDossier && renderLineInfo()}
          {brandDossier && !lineDossier && renderBrandInfo()}
        </View>
      )}
    </BeautyCard>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  brandInfo: {
    flex: 1,
  },
  brandName: {
    fontSize: 16,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  lineName: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.secondary,
    marginTop: 2,
  },
  expertBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: BeautyMinimalTheme.semantic.accent.background,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 12,
  },
  expertText: {
    fontSize: 12,
    fontWeight: '500',
    color: BeautyMinimalTheme.semantic.accent.primary,
    marginLeft: 4,
  },
  expandedContent: {
    borderTopWidth: 1,
    borderTopColor: BeautyMinimalTheme.semantic.border.subtle,
    paddingTop: 16,
  },
  tabContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    backgroundColor: BeautyMinimalTheme.semantic.background.secondary,
    borderRadius: 8,
    padding: 4,
  },
  tab: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    borderRadius: 6,
  },
  activeTab: {
    backgroundColor: BeautyMinimalTheme.semantic.background.primary,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: BeautyMinimalTheme.semantic.text.secondary,
  },
  activeTabText: {
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  contentSection: {
    gap: 16,
  },
  infoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  infoItem: {
    flex: 1,
    minWidth: '45%',
  },
  infoLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: BeautyMinimalTheme.semantic.text.secondary,
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  featuresSection: {
    gap: 8,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  tagContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  featureTag: {
    backgroundColor: BeautyMinimalTheme.semantic.accent.background,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  bestForTag: {
    backgroundColor: BeautyMinimalTheme.semantic.success.background,
  },
  featureText: {
    fontSize: 12,
    fontWeight: '500',
    color: BeautyMinimalTheme.semantic.accent.primary,
  },
  notesSection: {
    gap: 8,
  },
  notesText: {
    fontSize: 14,
    lineHeight: 20,
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  tipsSection: {
    gap: 8,
  },
  tipItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 8,
  },
  tipText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
    color: BeautyMinimalTheme.semantic.text.primary,
  },
});

export default BrandDossierCard;
