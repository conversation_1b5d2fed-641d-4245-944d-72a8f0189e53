import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';

interface BrandQuickInfoProps {
  mixingRatio?: string;
  processingTime?: string;
  maxDeveloperVolume?: number;
  coverageLevel?: 'light' | 'medium' | 'full' | 'maximum';
  liftingPower?: number;
  type?: 'permanent' | 'demi-permanent' | 'semi-permanent' | 'temporary' | 'bleaching';
}

export const BrandQuickInfo: React.FC<BrandQuickInfoProps> = ({
  mixingRatio,
  processingTime,
  maxDeveloperVolume,
  coverageLevel,
  liftingPower,
  type,
}) => {
  const getCoverageLevelColor = (level?: string) => {
    switch (level) {
      case 'maximum':
        return BeautyMinimalTheme.semantic.success.primary;
      case 'full':
        return BeautyMinimalTheme.semantic.accent.primary;
      case 'medium':
        return BeautyMinimalTheme.semantic.warning.primary;
      case 'light':
        return BeautyMinimalTheme.semantic.text.secondary;
      default:
        return BeautyMinimalTheme.semantic.text.secondary;
    }
  };

  const getTypeColor = (productType?: string) => {
    switch (productType) {
      case 'permanent':
        return BeautyMinimalTheme.semantic.success.primary;
      case 'demi-permanent':
        return BeautyMinimalTheme.semantic.accent.primary;
      case 'semi-permanent':
        return BeautyMinimalTheme.semantic.warning.primary;
      case 'temporary':
        return BeautyMinimalTheme.semantic.text.secondary;
      case 'bleaching':
        return BeautyMinimalTheme.semantic.error.primary;
      default:
        return BeautyMinimalTheme.semantic.text.secondary;
    }
  };

  const renderQuickInfoItem = (icon: string, label: string, value: string, color?: string) => (
    <View style={styles.quickInfoItem}>
      <Ionicons 
        name={icon as any} 
        size={14} 
        color={color || BeautyMinimalTheme.semantic.text.secondary} 
      />
      <Text style={styles.quickInfoLabel}>{label}:</Text>
      <Text style={[styles.quickInfoValue, color && { color }]}>{value}</Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.quickInfoGrid}>
        {mixingRatio && renderQuickInfoItem('flask', 'Mezcla', mixingRatio)}
        {processingTime && renderQuickInfoItem('time', 'Tiempo', processingTime)}
        {maxDeveloperVolume && renderQuickInfoItem('water', 'Max Vol', `${maxDeveloperVolume}vol`)}
        {type && renderQuickInfoItem('beaker', 'Tipo', type, getTypeColor(type))}
        {coverageLevel && renderQuickInfoItem('shield-checkmark', 'Cobertura', coverageLevel, getCoverageLevelColor(coverageLevel))}
        {liftingPower !== undefined && renderQuickInfoItem('trending-up', 'Aclaración', `${liftingPower} niveles`)}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: BeautyMinimalTheme.semantic.background.secondary,
    borderRadius: 8,
    padding: 12,
    marginTop: 8,
  },
  quickInfoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  quickInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    minWidth: '45%',
    gap: 4,
  },
  quickInfoLabel: {
    fontSize: 12,
    color: BeautyMinimalTheme.semantic.text.secondary,
    fontWeight: '500',
  },
  quickInfoValue: {
    fontSize: 12,
    color: BeautyMinimalTheme.semantic.text.primary,
    fontWeight: '600',
  },
});

export default BrandQuickInfo;
