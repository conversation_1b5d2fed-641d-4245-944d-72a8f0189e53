import React from 'react';
import { View, Text, StyleSheet, ScrollView, Modal } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { BeautyCard } from '@/components/beauty/BeautyCard';
import { BeautyButton } from '@/components/beauty/BeautyButton';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { 
  TransitionValidation, 
  TransitionStatus, 
  TransitionRecommendation 
} from '@/utils/smart-transitions';

interface SmartTransitionDialogProps {
  visible: boolean;
  validation: TransitionValidation | null;
  onProceed: () => void;
  onCancel: () => void;
  onRecommendationPress?: (recommendation: TransitionRecommendation) => void;
}

export const SmartTransitionDialog: React.FC<SmartTransitionDialogProps> = ({
  visible,
  validation,
  onProceed,
  onCancel,
  onRecommendationPress,
}) => {
  if (!validation) return null;

  const getStatusConfig = () => {
    switch (validation.status) {
      case TransitionStatus.PROCEED:
        return {
          icon: 'checkmark-circle',
          color: BeautyMinimalTheme.semantic.success.primary,
          backgroundColor: BeautyMinimalTheme.semantic.success.background,
        };
      case TransitionStatus.WARNING:
        return {
          icon: 'warning',
          color: BeautyMinimalTheme.semantic.warning.primary,
          backgroundColor: BeautyMinimalTheme.semantic.warning.background,
        };
      case TransitionStatus.CONFIRMATION:
        return {
          icon: 'help-circle',
          color: BeautyMinimalTheme.semantic.accent.primary,
          backgroundColor: BeautyMinimalTheme.semantic.accent.background,
        };
      case TransitionStatus.BLOCKED:
        return {
          icon: 'close-circle',
          color: BeautyMinimalTheme.semantic.error.primary,
          backgroundColor: BeautyMinimalTheme.semantic.error.background,
        };
    }
  };

  const getRiskLevelConfig = (riskLevel: string) => {
    switch (riskLevel) {
      case 'critical':
        return {
          color: BeautyMinimalTheme.semantic.error.primary,
          backgroundColor: BeautyMinimalTheme.semantic.error.background,
          text: 'CRÍTICO'
        };
      case 'high':
        return {
          color: BeautyMinimalTheme.semantic.error.secondary,
          backgroundColor: BeautyMinimalTheme.semantic.error.background,
          text: 'ALTO'
        };
      case 'medium':
        return {
          color: BeautyMinimalTheme.semantic.warning.primary,
          backgroundColor: BeautyMinimalTheme.semantic.warning.background,
          text: 'MEDIO'
        };
      case 'low':
        return {
          color: BeautyMinimalTheme.semantic.success.primary,
          backgroundColor: BeautyMinimalTheme.semantic.success.background,
          text: 'BAJO'
        };
      default:
        return {
          color: BeautyMinimalTheme.semantic.text.secondary,
          backgroundColor: BeautyMinimalTheme.semantic.background.secondary,
          text: 'DESCONOCIDO'
        };
    }
  };

  const getRecommendationIcon = (type: string) => {
    switch (type) {
      case 'action': return 'play-circle';
      case 'alternative': return 'swap-horizontal';
      case 'warning': return 'alert-circle';
      default: return 'information-circle';
    }
  };

  const statusConfig = getStatusConfig();
  const riskConfig = getRiskLevelConfig(validation.riskLevel);

  const renderRecommendation = (recommendation: TransitionRecommendation, index: number) => (
    <BeautyCard 
      key={recommendation.id} 
      variant="subtle" 
      style={styles.recommendationCard}
      onPress={() => onRecommendationPress?.(recommendation)}
    >
      <View style={styles.recommendationHeader}>
        <Ionicons 
          name={getRecommendationIcon(recommendation.type) as any} 
          size={20} 
          color={BeautyMinimalTheme.semantic.accent.primary} 
        />
        <Text style={styles.recommendationTitle}>{recommendation.title}</Text>
      </View>
      <Text style={styles.recommendationDescription}>{recommendation.description}</Text>
    </BeautyCard>
  );

  const renderActionButtons = () => {
    switch (validation.status) {
      case TransitionStatus.PROCEED:
        return (
          <BeautyButton
            title="Continuar"
            onPress={onProceed}
            variant="primary"
            style={styles.actionButton}
          />
        );
      
      case TransitionStatus.WARNING:
        return (
          <View style={styles.buttonContainer}>
            <BeautyButton
              title="Entendido, Continuar"
              onPress={onProceed}
              variant="primary"
              style={[styles.actionButton, { flex: 1 }]}
            />
            <BeautyButton
              title="Revisar"
              onPress={onCancel}
              variant="secondary"
              style={[styles.actionButton, { flex: 1, marginLeft: 8 }]}
            />
          </View>
        );
      
      case TransitionStatus.CONFIRMATION:
        return (
          <View style={styles.buttonContainer}>
            <BeautyButton
              title="Sí, Continuar"
              onPress={onProceed}
              variant="primary"
              style={[styles.actionButton, { flex: 1 }]}
            />
            <BeautyButton
              title="No, Revisar"
              onPress={onCancel}
              variant="secondary"
              style={[styles.actionButton, { flex: 1, marginLeft: 8 }]}
            />
          </View>
        );
      
      case TransitionStatus.BLOCKED:
        return (
          <BeautyButton
            title="Entendido"
            onPress={onCancel}
            variant="secondary"
            style={styles.actionButton}
          />
        );
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onCancel}
    >
      <View style={styles.overlay}>
        <View style={styles.container}>
          <BeautyCard variant="default" style={styles.dialog}>
            {/* Header */}
            <View style={styles.header}>
              <View style={[styles.statusIcon, { backgroundColor: statusConfig.backgroundColor }]}>
                <Ionicons 
                  name={statusConfig.icon as any} 
                  size={32} 
                  color={statusConfig.color} 
                />
              </View>
              
              <View style={styles.headerContent}>
                <Text style={[styles.title, { color: statusConfig.color }]}>
                  {validation.title}
                </Text>
                
                <View style={styles.riskBadge}>
                  <View style={[styles.riskIndicator, { backgroundColor: riskConfig.backgroundColor }]}>
                    <Text style={[styles.riskText, { color: riskConfig.color }]}>
                      RIESGO {riskConfig.text}
                    </Text>
                  </View>
                </View>
              </View>
            </View>

            {/* Message */}
            <Text style={styles.message}>{validation.message}</Text>

            {/* Details */}
            {validation.details && validation.details.length > 0 && (
              <View style={styles.detailsSection}>
                <Text style={styles.sectionTitle}>Detalles:</Text>
                {validation.details.map((detail, index) => (
                  <View key={index} style={styles.detailItem}>
                    <Ionicons 
                      name="ellipse" 
                      size={6} 
                      color={BeautyMinimalTheme.semantic.text.secondary} 
                    />
                    <Text style={styles.detailText}>{detail}</Text>
                  </View>
                ))}
              </View>
            )}

            {/* Recommendations */}
            {validation.recommendations && validation.recommendations.length > 0 && (
              <View style={styles.recommendationsSection}>
                <Text style={styles.sectionTitle}>Recomendaciones:</Text>
                <ScrollView 
                  style={styles.recommendationsScroll}
                  showsVerticalScrollIndicator={false}
                >
                  {validation.recommendations.map(renderRecommendation)}
                </ScrollView>
              </View>
            )}

            {/* Action Buttons */}
            <View style={styles.actionsSection}>
              {renderActionButtons()}
            </View>
          </BeautyCard>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  container: {
    width: '100%',
    maxWidth: 400,
    maxHeight: '80%',
  },
  dialog: {
    padding: 0,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    paddingBottom: 16,
  },
  statusIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  headerContent: {
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  riskBadge: {
    alignSelf: 'flex-start',
  },
  riskIndicator: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  riskText: {
    fontSize: 10,
    fontWeight: '600',
  },
  message: {
    fontSize: 16,
    lineHeight: 22,
    color: BeautyMinimalTheme.semantic.text.primary,
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  detailsSection: {
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: 8,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    paddingLeft: 8,
  },
  detailText: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.secondary,
    marginLeft: 8,
    flex: 1,
  },
  recommendationsSection: {
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  recommendationsScroll: {
    maxHeight: 200,
  },
  recommendationCard: {
    padding: 12,
    marginBottom: 8,
  },
  recommendationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  recommendationTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
    marginLeft: 8,
  },
  recommendationDescription: {
    fontSize: 12,
    color: BeautyMinimalTheme.semantic.text.secondary,
    lineHeight: 16,
  },
  actionsSection: {
    padding: 20,
    paddingTop: 0,
    borderTopWidth: 1,
    borderTopColor: BeautyMinimalTheme.semantic.border.subtle,
  },
  buttonContainer: {
    flexDirection: 'row',
  },
  actionButton: {
    marginVertical: 0,
  },
});

export default SmartTransitionDialog;
