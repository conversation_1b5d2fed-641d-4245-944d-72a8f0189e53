import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, Modal } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { BeautyCard } from '@/components/beauty/BeautyCard';
import { BeautyButton } from '@/components/beauty/BeautyButton';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { AlternativeRecommendation, ViabilityStatus } from '@/utils/viability-analyzer';
import { DesiredColorAnalysisResult } from '@/types/desired-analysis';

interface AlternativeRecommendationSystemProps {
  visible: boolean;
  alternatives: AlternativeRecommendation[];
  originalTarget: DesiredColorAnalysisResult;
  onSelectAlternative: (alternative: AlternativeRecommendation) => void;
  onKeepOriginal: () => void;
  onCancel: () => void;
}

export const AlternativeRecommendationSystem: React.FC<AlternativeRecommendationSystemProps> = ({
  visible,
  alternatives,
  originalTarget,
  onSelectAlternative,
  onKeepOriginal,
  onCancel,
}) => {
  const [selectedAlternative, setSelectedAlternative] = useState<AlternativeRecommendation | null>(null);
  const [showComparison, setShowComparison] = useState(false);

  const getViabilityColor = (status: ViabilityStatus) => {
    switch (status) {
      case ViabilityStatus.GREEN:
        return BeautyMinimalTheme.semantic.success.primary;
      case ViabilityStatus.AMBER:
        return BeautyMinimalTheme.semantic.warning.primary;
      case ViabilityStatus.RED:
        return BeautyMinimalTheme.semantic.error.primary;
    }
  };

  const getViabilityText = (status: ViabilityStatus) => {
    switch (status) {
      case ViabilityStatus.GREEN:
        return 'Proceso Seguro';
      case ViabilityStatus.AMBER:
        return 'Proceso Gradual';
      case ViabilityStatus.RED:
        return 'Alto Riesgo';
    }
  };

  const renderAlternativeCard = (alternative: AlternativeRecommendation, index: number) => {
    const isSelected = selectedAlternative?.targetColor === alternative.targetColor;
    const viabilityColor = getViabilityColor(alternative.viabilityImprovement);
    const viabilityText = getViabilityText(alternative.viabilityImprovement);

    return (
      <BeautyCard
        key={index}
        variant={isSelected ? "elevated" : "default"}
        style={[styles.alternativeCard, isSelected && styles.selectedCard]}
        onPress={() => setSelectedAlternative(isSelected ? null : alternative)}
      >
        <View style={styles.cardHeader}>
          <View style={styles.alternativeInfo}>
            <Text style={styles.alternativeTitle}>{alternative.targetColor}</Text>
            <Text style={styles.alternativeReason}>{alternative.reason}</Text>
          </View>
          
          <View style={[styles.viabilityBadge, { backgroundColor: viabilityColor + '20' }]}>
            <Ionicons 
              name={alternative.viabilityImprovement === ViabilityStatus.GREEN ? "checkmark-circle" : 
                    alternative.viabilityImprovement === ViabilityStatus.AMBER ? "warning" : "close-circle"} 
              size={16} 
              color={viabilityColor} 
            />
            <Text style={[styles.viabilityText, { color: viabilityColor }]}>{viabilityText}</Text>
          </View>
        </View>

        <Text style={styles.alternativeDescription}>{alternative.description}</Text>

        {isSelected && (
          <View style={styles.selectedActions}>
            <BeautyButton
              title="Seleccionar Esta Alternativa"
              onPress={() => onSelectAlternative(alternative)}
              variant="primary"
              size="sm"
              fullWidth
            />
            
            {showComparison && (
              <View style={styles.comparisonSection}>
                <Text style={styles.comparisonTitle}>Comparación con objetivo original:</Text>
                <View style={styles.comparisonGrid}>
                  <View style={styles.comparisonItem}>
                    <Text style={styles.comparisonLabel}>Color objetivo:</Text>
                    <Text style={styles.comparisonOriginal}>{originalTarget.general?.overallLevel}</Text>
                    <Text style={styles.comparisonArrow}>→</Text>
                    <Text style={styles.comparisonAlternative}>{alternative.targetColor}</Text>
                  </View>
                  
                  <View style={styles.comparisonItem}>
                    <Text style={styles.comparisonLabel}>Viabilidad:</Text>
                    <Text style={styles.comparisonOriginal}>Alto Riesgo</Text>
                    <Text style={styles.comparisonArrow}>→</Text>
                    <Text style={[styles.comparisonAlternative, { color: viabilityColor }]}>
                      {viabilityText}
                    </Text>
                  </View>
                </View>
              </View>
            )}
            
            <BeautyButton
              title={showComparison ? "Ocultar Comparación" : "Ver Comparación"}
              onPress={() => setShowComparison(!showComparison)}
              variant="secondary"
              size="sm"
              fullWidth
              style={styles.comparisonButton}
            />
          </View>
        )}
      </BeautyCard>
    );
  };

  const renderOriginalOption = () => (
    <BeautyCard variant="subtle" style={styles.originalCard}>
      <View style={styles.originalHeader}>
        <Ionicons name="target" size={24} color={BeautyMinimalTheme.semantic.text.primary} />
        <View style={styles.originalInfo}>
          <Text style={styles.originalTitle}>Mantener Objetivo Original</Text>
          <Text style={styles.originalSubtitle}>
            {originalTarget.general?.overallLevel} - {originalTarget.general?.overallTone}
          </Text>
        </View>
        <View style={[styles.viabilityBadge, { backgroundColor: BeautyMinimalTheme.semantic.error.background }]}>
          <Ionicons name="warning" size={16} color={BeautyMinimalTheme.semantic.error.primary} />
          <Text style={[styles.viabilityText, { color: BeautyMinimalTheme.semantic.error.primary }]}>
            Alto Riesgo
          </Text>
        </View>
      </View>

      <Text style={styles.originalDescription}>
        Proceder con el color original requiere aceptar los riesgos identificados. 
        Se recomienda documentar el consentimiento informado del cliente.
      </Text>

      <View style={styles.riskWarning}>
        <Ionicons name="alert-circle" size={16} color={BeautyMinimalTheme.semantic.error.primary} />
        <Text style={styles.riskWarningText}>
          Asegúrate de explicar todos los riesgos al cliente antes de proceder
        </Text>
      </View>

      <BeautyButton
        title="Mantener Objetivo Original"
        onPress={onKeepOriginal}
        variant="danger"
        fullWidth
        style={styles.originalButton}
      />
    </BeautyCard>
  );

  const renderRecommendationSummary = () => (
    <BeautyCard variant="subtle" style={styles.summaryCard}>
      <View style={styles.summaryHeader}>
        <Ionicons name="bulb" size={20} color={BeautyMinimalTheme.semantic.accent.primary} />
        <Text style={styles.summaryTitle}>Recomendación del Sistema</Text>
      </View>
      
      <Text style={styles.summaryText}>
        Basado en el análisis del cabello, estas alternativas ofrecen mejores resultados 
        con menor riesgo de daño. Considera especialmente las opciones marcadas como "Proceso Seguro".
      </Text>

      <View style={styles.summaryStats}>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{alternatives.filter(a => a.viabilityImprovement === ViabilityStatus.GREEN).length}</Text>
          <Text style={styles.statLabel}>Opciones Seguras</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{alternatives.filter(a => a.viabilityImprovement === ViabilityStatus.AMBER).length}</Text>
          <Text style={styles.statLabel}>Opciones Graduales</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{alternatives.length}</Text>
          <Text style={styles.statLabel}>Total Alternativas</Text>
        </View>
      </View>
    </BeautyCard>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onCancel}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <Text style={styles.headerTitle}>Alternativas Recomendadas</Text>
            <Text style={styles.headerSubtitle}>
              Opciones más seguras para lograr un resultado similar
            </Text>
          </View>
        </View>

        {/* Content */}
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Recommendation Summary */}
          {renderRecommendationSummary()}

          {/* Alternatives */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Alternativas Disponibles</Text>
            {alternatives.map(renderAlternativeCard)}
          </View>

          {/* Original Option */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Opción Original</Text>
            {renderOriginalOption()}
          </View>
        </ScrollView>

        {/* Actions */}
        <View style={styles.actions}>
          <BeautyButton
            title="Cancelar"
            onPress={onCancel}
            variant="secondary"
            style={styles.actionButton}
          />
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BeautyMinimalTheme.semantic.background.primary,
  },
  header: {
    padding: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: BeautyMinimalTheme.semantic.border.subtle,
  },
  headerContent: {
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: 4,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 16,
    color: BeautyMinimalTheme.semantic.text.secondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: 16,
  },
  summaryCard: {
    padding: 16,
    marginBottom: 24,
  },
  summaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
    marginLeft: 8,
  },
  summaryText: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.secondary,
    lineHeight: 20,
    marginBottom: 16,
  },
  summaryStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: '700',
    color: BeautyMinimalTheme.semantic.accent.primary,
  },
  statLabel: {
    fontSize: 12,
    color: BeautyMinimalTheme.semantic.text.secondary,
    textAlign: 'center',
  },
  alternativeCard: {
    marginBottom: 12,
    padding: 16,
  },
  selectedCard: {
    borderColor: BeautyMinimalTheme.semantic.accent.primary,
    borderWidth: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  alternativeInfo: {
    flex: 1,
  },
  alternativeTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: 2,
  },
  alternativeReason: {
    fontSize: 12,
    color: BeautyMinimalTheme.semantic.text.secondary,
  },
  viabilityBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginLeft: 12,
  },
  viabilityText: {
    fontSize: 10,
    fontWeight: '600',
    marginLeft: 4,
  },
  alternativeDescription: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.secondary,
    lineHeight: 20,
    marginBottom: 12,
  },
  selectedActions: {
    marginTop: 8,
    gap: 8,
  },
  comparisonSection: {
    marginTop: 12,
    padding: 12,
    backgroundColor: BeautyMinimalTheme.semantic.background.secondary,
    borderRadius: 8,
  },
  comparisonTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: 8,
  },
  comparisonGrid: {
    gap: 8,
  },
  comparisonItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  comparisonLabel: {
    fontSize: 12,
    color: BeautyMinimalTheme.semantic.text.secondary,
    width: 80,
  },
  comparisonOriginal: {
    fontSize: 12,
    color: BeautyMinimalTheme.semantic.text.primary,
    flex: 1,
  },
  comparisonArrow: {
    fontSize: 12,
    color: BeautyMinimalTheme.semantic.text.secondary,
  },
  comparisonAlternative: {
    fontSize: 12,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
    flex: 1,
  },
  comparisonButton: {
    marginTop: 4,
  },
  originalCard: {
    padding: 16,
  },
  originalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  originalInfo: {
    flex: 1,
    marginLeft: 12,
  },
  originalTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  originalSubtitle: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.secondary,
  },
  originalDescription: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.secondary,
    lineHeight: 20,
    marginBottom: 12,
  },
  riskWarning: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: BeautyMinimalTheme.semantic.error.background,
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  riskWarningText: {
    fontSize: 12,
    color: BeautyMinimalTheme.semantic.error.primary,
    marginLeft: 8,
    flex: 1,
  },
  originalButton: {
    marginVertical: 0,
  },
  actions: {
    padding: 20,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: BeautyMinimalTheme.semantic.border.subtle,
  },
  actionButton: {
    marginVertical: 0,
  },
});

export default AlternativeRecommendationSystem;
