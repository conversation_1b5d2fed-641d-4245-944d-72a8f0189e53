import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, Modal } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { BeautyCard } from '@/components/beauty/BeautyCard';
import { BeautyButton } from '@/components/beauty/BeautyButton';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { SmartViabilityAnalysis, SessionDetail } from '@/utils/viability-analyzer';

interface AmberProcessFlowProps {
  visible: boolean;
  analysis: SmartViabilityAnalysis;
  onAcceptPlan: () => void;
  onViewAlternatives: () => void;
  onCancel: () => void;
}

export const AmberProcessFlow: React.FC<AmberProcessFlowProps> = ({
  visible,
  analysis,
  onAcceptPlan,
  onViewAlternatives,
  onCancel,
}) => {
  const [selectedSession, setSelectedSession] = useState<number | null>(null);
  const [showTimelineView, setShowTimelineView] = useState(false);

  const renderSessionCard = (session: SessionDetail, index: number) => {
    const isSelected = selectedSession === index;
    const isFirst = index === 0;
    const isLast = index === analysis.sessionPlan.sessionDetails.length - 1;

    return (
      <BeautyCard 
        key={index}
        variant={isSelected ? "elevated" : "default"}
        style={[styles.sessionCard, isSelected && styles.selectedSessionCard]}
        onPress={() => setSelectedSession(isSelected ? null : index)}
      >
        <View style={styles.sessionHeader}>
          <View style={styles.sessionNumberContainer}>
            <View style={[styles.sessionNumber, isFirst && styles.firstSession, isLast && styles.lastSession]}>
              <Text style={styles.sessionNumberText}>{session.sessionNumber}</Text>
            </View>
            {!isLast && <View style={styles.sessionConnector} />}
          </View>
          
          <View style={styles.sessionInfo}>
            <Text style={styles.sessionTitle}>{session.description}</Text>
            <View style={styles.sessionMeta}>
              <View style={styles.metaItem}>
                <Ionicons name="time" size={14} color={BeautyMinimalTheme.semantic.text.secondary} />
                <Text style={styles.metaText}>{session.estimatedTime} min</Text>
              </View>
              {session.waitTime && (
                <View style={styles.metaItem}>
                  <Ionicons name="calendar" size={14} color={BeautyMinimalTheme.semantic.text.secondary} />
                  <Text style={styles.metaText}>Esperar {session.waitTime} días</Text>
                </View>
              )}
            </View>
          </View>

          <Ionicons 
            name={isSelected ? "chevron-up" : "chevron-down"} 
            size={20} 
            color={BeautyMinimalTheme.semantic.text.secondary} 
          />
        </View>

        {isSelected && (
          <View style={styles.sessionDetails}>
            <View style={styles.detailSection}>
              <Text style={styles.detailLabel}>Resultado Esperado:</Text>
              <Text style={styles.detailText}>{session.expectedResult}</Text>
            </View>
            
            {session.process && session.process.length > 0 && (
              <View style={styles.detailSection}>
                <Text style={styles.detailLabel}>Procesos:</Text>
                {session.process.map((process, idx) => (
                  <View key={idx} style={styles.processItem}>
                    <Ionicons name="ellipse" size={6} color={BeautyMinimalTheme.semantic.accent.primary} />
                    <Text style={styles.processText}>{process}</Text>
                  </View>
                ))}
              </View>
            )}

            {session.waitTime && (
              <View style={styles.waitTimeNotice}>
                <Ionicons name="information-circle" size={16} color={BeautyMinimalTheme.semantic.warning.primary} />
                <Text style={styles.waitTimeText}>
                  Es importante respetar el tiempo de espera para la salud del cabello
                </Text>
              </View>
            )}
          </View>
        )}
      </BeautyCard>
    );
  };

  const renderTimelineView = () => (
    <View style={styles.timelineContainer}>
      <Text style={styles.timelineTitle}>Cronograma del Proceso</Text>
      
      <View style={styles.timeline}>
        {analysis.sessionPlan.sessionDetails.map((session, index) => {
          const totalDays = analysis.sessionPlan.sessionDetails
            .slice(0, index + 1)
            .reduce((sum, s) => sum + (s.waitTime || 0), 0);

          return (
            <View key={index} style={styles.timelineItem}>
              <View style={styles.timelineDate}>
                <Text style={styles.timelineDateText}>
                  {index === 0 ? 'Hoy' : `+${totalDays} días`}
                </Text>
              </View>
              <View style={styles.timelineContent}>
                <View style={styles.timelineDot} />
                <Text style={styles.timelineSessionText}>Sesión {session.sessionNumber}</Text>
                <Text style={styles.timelineDescText}>{session.description}</Text>
              </View>
            </View>
          );
        })}
      </View>

      <View style={styles.timelineSummary}>
        <Text style={styles.summaryText}>
          Proceso completo: {analysis.sessionPlan.estimatedTimeframe}
        </Text>
      </View>
    </View>
  );

  const renderCostEstimate = () => {
    const sessionCount = analysis.sessionPlan.totalSessions;
    const avgSessionTime = analysis.sessionPlan.sessionDetails.reduce(
      (sum, session) => sum + session.estimatedTime, 0
    ) / sessionCount;

    return (
      <BeautyCard variant="subtle" style={styles.costCard}>
        <View style={styles.costHeader}>
          <Ionicons name="calculator" size={20} color={BeautyMinimalTheme.semantic.accent.primary} />
          <Text style={styles.costTitle}>Estimación del Servicio</Text>
        </View>
        
        <View style={styles.costDetails}>
          <View style={styles.costItem}>
            <Text style={styles.costLabel}>Total de sesiones:</Text>
            <Text style={styles.costValue}>{sessionCount}</Text>
          </View>
          <View style={styles.costItem}>
            <Text style={styles.costLabel}>Tiempo promedio:</Text>
            <Text style={styles.costValue}>{Math.round(avgSessionTime)} min</Text>
          </View>
          <View style={styles.costItem}>
            <Text style={styles.costLabel}>Duración total:</Text>
            <Text style={styles.costValue}>{analysis.sessionPlan.estimatedTimeframe}</Text>
          </View>
        </View>
      </BeautyCard>
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onCancel}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <View style={styles.statusBadge}>
              <Ionicons name="warning" size={20} color={BeautyMinimalTheme.semantic.warning.primary} />
              <Text style={styles.statusText}>PROCESO GRADUAL</Text>
            </View>
            <Text style={styles.headerTitle}>Plan de {analysis.sessionPlan.totalSessions} Sesiones</Text>
            <Text style={styles.headerSubtitle}>
              Proceso seguro y controlado para obtener el mejor resultado
            </Text>
          </View>
          
          <View style={styles.headerActions}>
            <BeautyButton
              title={showTimelineView ? "Lista" : "Cronograma"}
              onPress={() => setShowTimelineView(!showTimelineView)}
              variant="secondary"
              size="sm"
              style={styles.viewToggle}
            />
          </View>
        </View>

        {/* Content */}
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {showTimelineView ? renderTimelineView() : (
            <View style={styles.sessionsList}>
              <Text style={styles.sectionTitle}>Sesiones Programadas</Text>
              {analysis.sessionPlan.sessionDetails.map(renderSessionCard)}
            </View>
          )}

          {renderCostEstimate()}

          {/* Professional Notes */}
          <BeautyCard variant="subtle" style={styles.notesCard}>
            <View style={styles.notesHeader}>
              <Ionicons name="bulb" size={20} color={BeautyMinimalTheme.semantic.accent.primary} />
              <Text style={styles.notesTitle}>Notas Profesionales</Text>
            </View>
            <Text style={styles.notesText}>{analysis.professionalNotes}</Text>
          </BeautyCard>

          {/* Risk Factors */}
          {analysis.riskFactors.length > 0 && (
            <BeautyCard variant="subtle" style={styles.risksCard}>
              <View style={styles.risksHeader}>
                <Ionicons name="shield-checkmark" size={20} color={BeautyMinimalTheme.semantic.warning.primary} />
                <Text style={styles.risksTitle}>Factores de Riesgo Considerados</Text>
              </View>
              {analysis.riskFactors.map((risk, index) => (
                <View key={index} style={styles.riskItem}>
                  <View style={[styles.riskSeverity, { backgroundColor: getRiskColor(risk.severity) }]}>
                    <Text style={styles.riskSeverityText}>{risk.severity.toUpperCase()}</Text>
                  </View>
                  <Text style={styles.riskDescription}>{risk.description}</Text>
                </View>
              ))}
            </BeautyCard>
          )}
        </ScrollView>

        {/* Actions */}
        <View style={styles.actions}>
          <BeautyButton
            title="Ver Alternativas"
            onPress={onViewAlternatives}
            variant="secondary"
            style={[styles.actionButton, { flex: 1 }]}
          />
          <BeautyButton
            title="Aceptar Plan Gradual"
            onPress={onAcceptPlan}
            variant="primary"
            style={[styles.actionButton, { flex: 2, marginLeft: 12 }]}
          />
        </View>
      </View>
    </Modal>
  );
};

const getRiskColor = (severity: string) => {
  switch (severity) {
    case 'critical': return BeautyMinimalTheme.semantic.error.primary;
    case 'high': return BeautyMinimalTheme.semantic.error.secondary;
    case 'medium': return BeautyMinimalTheme.semantic.warning.primary;
    case 'low': return BeautyMinimalTheme.semantic.accent.primary;
    default: return BeautyMinimalTheme.semantic.text.secondary;
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BeautyMinimalTheme.semantic.background.primary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    padding: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: BeautyMinimalTheme.semantic.border.subtle,
  },
  headerContent: {
    flex: 1,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: BeautyMinimalTheme.semantic.warning.background,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    alignSelf: 'flex-start',
    marginBottom: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.warning.primary,
    marginLeft: 6,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: BeautyMinimalTheme.semantic.text.secondary,
    lineHeight: 22,
  },
  headerActions: {
    marginLeft: 16,
  },
  viewToggle: {
    minWidth: 100,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: 16,
  },
  sessionsList: {
    marginBottom: 24,
  },
  sessionCard: {
    marginBottom: 12,
    padding: 16,
  },
  selectedSessionCard: {
    borderColor: BeautyMinimalTheme.semantic.accent.primary,
    borderWidth: 2,
  },
  sessionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sessionNumberContainer: {
    alignItems: 'center',
    marginRight: 16,
  },
  sessionNumber: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: BeautyMinimalTheme.semantic.accent.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  firstSession: {
    backgroundColor: BeautyMinimalTheme.semantic.success.primary,
  },
  lastSession: {
    backgroundColor: BeautyMinimalTheme.semantic.warning.primary,
  },
  sessionNumberText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  sessionConnector: {
    width: 2,
    height: 20,
    backgroundColor: BeautyMinimalTheme.semantic.border.default,
    marginTop: 4,
  },
  sessionInfo: {
    flex: 1,
  },
  sessionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: 6,
  },
  sessionMeta: {
    flexDirection: 'row',
    gap: 16,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metaText: {
    fontSize: 12,
    color: BeautyMinimalTheme.semantic.text.secondary,
  },
  sessionDetails: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: BeautyMinimalTheme.semantic.border.subtle,
  },
  detailSection: {
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: 4,
  },
  detailText: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.secondary,
    lineHeight: 20,
  },
  processItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    paddingLeft: 8,
  },
  processText: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.secondary,
    marginLeft: 8,
  },
  waitTimeNotice: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: BeautyMinimalTheme.semantic.warning.background,
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  waitTimeText: {
    fontSize: 12,
    color: BeautyMinimalTheme.semantic.warning.primary,
    marginLeft: 8,
    flex: 1,
  },
  timelineContainer: {
    marginBottom: 24,
  },
  timelineTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: 16,
  },
  timeline: {
    paddingLeft: 20,
  },
  timelineItem: {
    flexDirection: 'row',
    marginBottom: 24,
  },
  timelineDate: {
    width: 80,
    alignItems: 'center',
  },
  timelineDateText: {
    fontSize: 12,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.accent.primary,
  },
  timelineContent: {
    flex: 1,
    position: 'relative',
  },
  timelineDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: BeautyMinimalTheme.semantic.accent.primary,
    position: 'absolute',
    left: -26,
    top: 2,
  },
  timelineSessionText: {
    fontSize: 16,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: 2,
  },
  timelineDescText: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.secondary,
  },
  timelineSummary: {
    marginTop: 16,
    padding: 16,
    backgroundColor: BeautyMinimalTheme.semantic.background.secondary,
    borderRadius: 8,
  },
  summaryText: {
    fontSize: 14,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
    textAlign: 'center',
  },
  costCard: {
    marginBottom: 16,
    padding: 16,
  },
  costHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  costTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
    marginLeft: 8,
  },
  costDetails: {
    gap: 8,
  },
  costItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  costLabel: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.secondary,
  },
  costValue: {
    fontSize: 14,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  notesCard: {
    marginBottom: 16,
    padding: 16,
  },
  notesHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  notesTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
    marginLeft: 8,
  },
  notesText: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.secondary,
    lineHeight: 20,
  },
  risksCard: {
    marginBottom: 16,
    padding: 16,
  },
  risksHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  risksTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
    marginLeft: 8,
  },
  riskItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  riskSeverity: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 12,
  },
  riskSeverityText: {
    fontSize: 10,
    fontWeight: '600',
    color: 'white',
  },
  riskDescription: {
    flex: 1,
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.secondary,
  },
  actions: {
    flexDirection: 'row',
    padding: 20,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: BeautyMinimalTheme.semantic.border.subtle,
  },
  actionButton: {
    marginVertical: 0,
  },
});

export default AmberProcessFlow;
