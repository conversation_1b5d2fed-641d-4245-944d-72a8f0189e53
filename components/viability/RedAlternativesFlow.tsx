import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, Modal } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { BeautyCard } from '@/components/beauty/BeautyCard';
import { BeautyButton } from '@/components/beauty/BeautyButton';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { SmartViabilityAnalysis } from '@/utils/viability-analyzer';
import { AlternativeRecommendation, ViabilityStatus } from '@/types/alternative-recommendations';

interface RedAlternativesFlowProps {
  visible: boolean;
  analysis: SmartViabilityAnalysis;
  onSelectAlternative: (alternative: AlternativeRecommendation) => void;
  onRequestTreatment: () => void;
  onProceedWithRisk: () => void;
  onCancel: () => void;
}

export const RedAlternativesFlow: React.FC<RedAlternativesFlowProps> = ({
  visible,
  analysis,
  onSelectAlternative,
  onRequestTreatment,
  onProceedWithRisk,
  onCancel,
}) => {
  const [selectedAlternative, setSelectedAlternative] = useState<AlternativeRecommendation | null>(null);
  const [showRiskDetails, setShowRiskDetails] = useState(false);

  const criticalRisks = analysis.riskFactors.filter(risk => risk.severity === 'critical');
  const highRisks = analysis.riskFactors.filter(risk => risk.severity === 'high');
  const hasCriticalRisks = criticalRisks.length > 0;

  const getAlternativeStatusColor = (status: ViabilityStatus) => {
    switch (status) {
      case ViabilityStatus.GREEN:
        return BeautyMinimalTheme.semantic.success.primary;
      case ViabilityStatus.AMBER:
        return BeautyMinimalTheme.semantic.warning.primary;
      default:
        return BeautyMinimalTheme.semantic.text.secondary;
    }
  };

  const getAlternativeStatusText = (status: ViabilityStatus) => {
    switch (status) {
      case ViabilityStatus.GREEN:
        return 'SEGURO';
      case ViabilityStatus.AMBER:
        return 'GRADUAL';
      default:
        return 'EVALUAR';
    }
  };

  const renderRiskSummary = () => (
    <BeautyCard variant="subtle" style={styles.riskSummaryCard}>
      <View style={styles.riskSummaryHeader}>
        <Ionicons name="warning" size={24} color={BeautyMinimalTheme.semantic.error.primary} />
        <View style={styles.riskSummaryContent}>
          <Text style={styles.riskSummaryTitle}>Riesgos Detectados</Text>
          <Text style={styles.riskSummarySubtitle}>
            {criticalRisks.length} críticos, {highRisks.length} altos
          </Text>
        </View>
        <BeautyButton
          title={showRiskDetails ? "Ocultar" : "Ver Detalles"}
          onPress={() => setShowRiskDetails(!showRiskDetails)}
          variant="secondary"
          size="sm"
        />
      </View>

      {showRiskDetails && (
        <View style={styles.riskDetails}>
          {analysis.riskFactors.map((risk, index) => (
            <View key={index} style={styles.riskItem}>
              <View style={[styles.riskSeverityBadge, { backgroundColor: getRiskColor(risk.severity) }]}>
                <Text style={styles.riskSeverityText}>{risk.severity.toUpperCase()}</Text>
              </View>
              <View style={styles.riskContent}>
                <Text style={styles.riskDescription}>{risk.description}</Text>
                {risk.mitigation && (
                  <Text style={styles.riskMitigation}>💡 {risk.mitigation}</Text>
                )}
              </View>
            </View>
          ))}
        </View>
      )}
    </BeautyCard>
  );

  const renderAlternativeCard = (alternative: AlternativeRecommendation, index: number) => {
    const isSelected = selectedAlternative?.targetColor === alternative.targetColor;
    const statusColor = getAlternativeStatusColor(alternative.viabilityImprovement);
    const statusText = getAlternativeStatusText(alternative.viabilityImprovement);

    return (
      <BeautyCard
        key={index}
        variant={isSelected ? "elevated" : "default"}
        style={[styles.alternativeCard, isSelected && styles.selectedAlternativeCard]}
        onPress={() => setSelectedAlternative(isSelected ? null : alternative)}
      >
        <View style={styles.alternativeHeader}>
          <View style={styles.alternativeInfo}>
            <Text style={styles.alternativeTitle}>{alternative.targetColor}</Text>
            <Text style={styles.alternativeReason}>{alternative.reason}</Text>
          </View>
          
          <View style={[styles.statusBadge, { backgroundColor: statusColor + '20' }]}>
            <Text style={[styles.statusText, { color: statusColor }]}>{statusText}</Text>
          </View>
        </View>

        <Text style={styles.alternativeDescription}>{alternative.description}</Text>

        {isSelected && (
          <View style={styles.alternativeActions}>
            <BeautyButton
              title="Seleccionar Esta Alternativa"
              onPress={() => onSelectAlternative(alternative)}
              variant="primary"
              size="sm"
              fullWidth
            />
          </View>
        )}
      </BeautyCard>
    );
  };

  const renderTreatmentOption = () => (
    <BeautyCard variant="subtle" style={styles.treatmentCard}>
      <View style={styles.treatmentHeader}>
        <Ionicons name="medical" size={24} color={BeautyMinimalTheme.semantic.accent.primary} />
        <View style={styles.treatmentContent}>
          <Text style={styles.treatmentTitle}>Tratamiento Reconstructivo</Text>
          <Text style={styles.treatmentSubtitle}>
            Prepara el cabello para el cambio deseado
          </Text>
        </View>
      </View>

      <Text style={styles.treatmentDescription}>
        Un tratamiento reconstructivo puede mejorar significativamente la condición del cabello, 
        reduciendo los riesgos y permitiendo lograr el color deseado de forma más segura.
      </Text>

      <View style={styles.treatmentBenefits}>
        <Text style={styles.benefitsTitle}>Beneficios del tratamiento:</Text>
        <View style={styles.benefitItem}>
          <Ionicons name="checkmark-circle" size={16} color={BeautyMinimalTheme.semantic.success.primary} />
          <Text style={styles.benefitText}>Fortalece la estructura capilar</Text>
        </View>
        <View style={styles.benefitItem}>
          <Ionicons name="checkmark-circle" size={16} color={BeautyMinimalTheme.semantic.success.primary} />
          <Text style={styles.benefitText}>Reduce la porosidad excesiva</Text>
        </View>
        <View style={styles.benefitItem}>
          <Ionicons name="checkmark-circle" size={16} color={BeautyMinimalTheme.semantic.success.primary} />
          <Text style={styles.benefitText}>Mejora la retención del color</Text>
        </View>
      </View>

      <BeautyButton
        title="Solicitar Tratamiento Previo"
        onPress={onRequestTreatment}
        variant="secondary"
        fullWidth
        style={styles.treatmentButton}
      />
    </BeautyCard>
  );

  const renderRiskProceedOption = () => {
    if (hasCriticalRisks) return null; // No permitir proceder con riesgos críticos

    return (
      <BeautyCard variant="subtle" style={styles.riskProceedCard}>
        <View style={styles.riskProceedHeader}>
          <Ionicons name="alert-circle" size={24} color={BeautyMinimalTheme.semantic.error.primary} />
          <View style={styles.riskProceedContent}>
            <Text style={styles.riskProceedTitle}>Proceder Bajo Riesgo</Text>
            <Text style={styles.riskProceedSubtitle}>
              Solo con consentimiento informado del cliente
            </Text>
          </View>
        </View>

        <Text style={styles.riskProceedDescription}>
          Si el cliente comprende completamente los riesgos y desea proceder, 
          es importante documentar el consentimiento informado y tomar precauciones adicionales.
        </Text>

        <View style={styles.riskProceedWarning}>
          <Ionicons name="warning" size={16} color={BeautyMinimalTheme.semantic.error.primary} />
          <Text style={styles.warningText}>
            Asegúrate de explicar todos los riesgos y obtener consentimiento por escrito
          </Text>
        </View>

        <BeautyButton
          title="Proceder Bajo Riesgo"
          onPress={onProceedWithRisk}
          variant="danger"
          fullWidth
          style={styles.riskProceedButton}
        />
      </BeautyCard>
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onCancel}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <View style={styles.statusIndicator}>
              <Ionicons name="close-circle" size={24} color={BeautyMinimalTheme.semantic.error.primary} />
              <Text style={styles.statusTitle}>PROCESO NO RECOMENDADO</Text>
            </View>
            <Text style={styles.headerTitle}>Alternativas Seguras</Text>
            <Text style={styles.headerSubtitle}>
              Explora opciones que protegen la salud del cabello
            </Text>
          </View>
        </View>

        {/* Content */}
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Risk Summary */}
          {renderRiskSummary()}

          {/* Alternatives */}
          {analysis.alternatives && analysis.alternatives.length > 0 && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Alternativas Recomendadas</Text>
              <Text style={styles.sectionSubtitle}>
                Opciones más seguras que pueden lograr un resultado similar
              </Text>
              {analysis.alternatives.map(renderAlternativeCard)}
            </View>
          )}

          {/* Treatment Option */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Opción de Tratamiento</Text>
            {renderTreatmentOption()}
          </View>

          {/* Risk Proceed Option */}
          {!hasCriticalRisks && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Última Opción</Text>
              {renderRiskProceedOption()}
            </View>
          )}

          {/* Professional Notes */}
          <BeautyCard variant="subtle" style={styles.notesCard}>
            <View style={styles.notesHeader}>
              <Ionicons name="document-text" size={20} color={BeautyMinimalTheme.semantic.accent.primary} />
              <Text style={styles.notesTitle}>Notas Profesionales</Text>
            </View>
            <Text style={styles.notesText}>{analysis.professionalNotes}</Text>
          </BeautyCard>
        </ScrollView>

        {/* Actions */}
        <View style={styles.actions}>
          <BeautyButton
            title="Volver al Diagnóstico"
            onPress={onCancel}
            variant="secondary"
            style={styles.actionButton}
          />
        </View>
      </View>
    </Modal>
  );
};

const getRiskColor = (severity: string) => {
  switch (severity) {
    case 'critical': return BeautyMinimalTheme.semantic.error.primary;
    case 'high': return BeautyMinimalTheme.semantic.error.secondary;
    case 'medium': return BeautyMinimalTheme.semantic.warning.primary;
    case 'low': return BeautyMinimalTheme.semantic.accent.primary;
    default: return BeautyMinimalTheme.semantic.text.secondary;
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BeautyMinimalTheme.semantic.background.primary,
  },
  header: {
    padding: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: BeautyMinimalTheme.semantic.border.subtle,
  },
  headerContent: {
    alignItems: 'center',
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: BeautyMinimalTheme.semantic.error.background,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginBottom: 16,
  },
  statusTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.error.primary,
    marginLeft: 8,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: 4,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 16,
    color: BeautyMinimalTheme.semantic.text.secondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: 4,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.secondary,
    marginBottom: 16,
    lineHeight: 20,
  },
  riskSummaryCard: {
    padding: 16,
    marginBottom: 24,
  },
  riskSummaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  riskSummaryContent: {
    flex: 1,
    marginLeft: 12,
  },
  riskSummaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  riskSummarySubtitle: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.secondary,
  },
  riskDetails: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: BeautyMinimalTheme.semantic.border.subtle,
  },
  riskItem: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  riskSeverityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 12,
    alignSelf: 'flex-start',
  },
  riskSeverityText: {
    fontSize: 10,
    fontWeight: '600',
    color: 'white',
  },
  riskContent: {
    flex: 1,
  },
  riskDescription: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: 4,
  },
  riskMitigation: {
    fontSize: 12,
    color: BeautyMinimalTheme.semantic.text.secondary,
    fontStyle: 'italic',
  },
  alternativeCard: {
    marginBottom: 12,
    padding: 16,
  },
  selectedAlternativeCard: {
    borderColor: BeautyMinimalTheme.semantic.accent.primary,
    borderWidth: 2,
  },
  alternativeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  alternativeInfo: {
    flex: 1,
  },
  alternativeTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: 2,
  },
  alternativeReason: {
    fontSize: 12,
    color: BeautyMinimalTheme.semantic.text.secondary,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginLeft: 12,
  },
  statusText: {
    fontSize: 10,
    fontWeight: '600',
  },
  alternativeDescription: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.secondary,
    lineHeight: 20,
    marginBottom: 12,
  },
  alternativeActions: {
    marginTop: 8,
  },
  treatmentCard: {
    padding: 16,
  },
  treatmentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  treatmentContent: {
    flex: 1,
    marginLeft: 12,
  },
  treatmentTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  treatmentSubtitle: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.secondary,
  },
  treatmentDescription: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.secondary,
    lineHeight: 20,
    marginBottom: 16,
  },
  treatmentBenefits: {
    marginBottom: 16,
  },
  benefitsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: 8,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  benefitText: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.secondary,
    marginLeft: 8,
  },
  treatmentButton: {
    marginVertical: 0,
  },
  riskProceedCard: {
    padding: 16,
  },
  riskProceedHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  riskProceedContent: {
    flex: 1,
    marginLeft: 12,
  },
  riskProceedTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  riskProceedSubtitle: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.secondary,
  },
  riskProceedDescription: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.secondary,
    lineHeight: 20,
    marginBottom: 12,
  },
  riskProceedWarning: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: BeautyMinimalTheme.semantic.error.background,
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  warningText: {
    fontSize: 12,
    color: BeautyMinimalTheme.semantic.error.primary,
    marginLeft: 8,
    flex: 1,
  },
  riskProceedButton: {
    marginVertical: 0,
  },
  notesCard: {
    padding: 16,
    marginBottom: 16,
  },
  notesHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  notesTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
    marginLeft: 8,
  },
  notesText: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.secondary,
    lineHeight: 20,
  },
  actions: {
    padding: 20,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: BeautyMinimalTheme.semantic.border.subtle,
  },
  actionButton: {
    marginVertical: 0,
  },
});

export default RedAlternativesFlow;
