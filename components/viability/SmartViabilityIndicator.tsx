import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { BeautyCard } from '@/components/beauty/BeautyCard';
import { BeautyButton } from '@/components/beauty/BeautyButton';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { SmartViabilityAnalysis, ViabilityStatus, SessionDetail, RiskFactor } from '@/utils/viability-analyzer';

interface SmartViabilityIndicatorProps {
  analysis: SmartViabilityAnalysis | null;
  onProceed?: () => void;
  onViewAlternatives?: () => void;
  onRequestTreatment?: () => void;
}

export const SmartViabilityIndicator: React.FC<SmartViabilityIndicatorProps> = ({
  analysis,
  onProceed,
  onViewAlternatives,
  onRequestTreatment,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [activeTab, setActiveTab] = useState<'plan' | 'risks' | 'notes'>('plan');

  if (!analysis) return null;

  const getStatusConfig = () => {
    switch (analysis.status) {
      case ViabilityStatus.GREEN:
        return {
          color: BeautyMinimalTheme.semantic.success.primary,
          backgroundColor: BeautyMinimalTheme.semantic.success.background,
          icon: 'checkmark-circle',
          title: 'VERDE - Proceso Directo',
          subtitle: 'Coloración segura en una sesión',
          actionText: 'Proceder con Formulación',
        };
      case ViabilityStatus.AMBER:
        return {
          color: BeautyMinimalTheme.semantic.warning.primary,
          backgroundColor: BeautyMinimalTheme.semantic.warning.background,
          icon: 'warning',
          title: 'ÁMBAR - Proceso Gradual',
          subtitle: `${analysis.sessionPlan.totalSessions} sesiones recomendadas`,
          actionText: 'Ver Plan de Sesiones',
        };
      case ViabilityStatus.RED:
        return {
          color: BeautyMinimalTheme.semantic.error.primary,
          backgroundColor: BeautyMinimalTheme.semantic.error.background,
          icon: 'close-circle',
          title: 'ROJO - No Recomendado',
          subtitle: 'Riesgo significativo de daño',
          actionText: 'Ver Alternativas',
        };
    }
  };

  const config = getStatusConfig();

  const renderSessionPlan = () => (
    <View style={styles.tabContent}>
      <View style={styles.planHeader}>
        <Text style={styles.planTitle}>Plan de {analysis.sessionPlan.totalSessions} Sesiones</Text>
        <Text style={styles.planTimeframe}>Tiempo estimado: {analysis.sessionPlan.estimatedTimeframe}</Text>
      </View>
      
      {analysis.sessionPlan.sessionDetails.map((session, index) => (
        <BeautyCard key={index} variant="subtle" style={styles.sessionCard}>
          <View style={styles.sessionHeader}>
            <View style={styles.sessionNumber}>
              <Text style={styles.sessionNumberText}>{session.sessionNumber}</Text>
            </View>
            <View style={styles.sessionInfo}>
              <Text style={styles.sessionDescription}>{session.description}</Text>
              <Text style={styles.sessionTime}>{session.estimatedTime} min</Text>
            </View>
          </View>
          
          <Text style={styles.sessionResult}>Resultado esperado: {session.expectedResult}</Text>
          
          {session.waitTime && (
            <View style={styles.waitTimeContainer}>
              <Ionicons name="time" size={14} color={BeautyMinimalTheme.semantic.text.secondary} />
              <Text style={styles.waitTimeText}>Esperar {session.waitTime} días antes de la siguiente sesión</Text>
            </View>
          )}
        </BeautyCard>
      ))}
    </View>
  );

  const renderRiskFactors = () => (
    <View style={styles.tabContent}>
      {analysis.riskFactors.length === 0 ? (
        <Text style={styles.noRisksText}>No se detectaron factores de riesgo significativos</Text>
      ) : (
        analysis.riskFactors.map((risk, index) => (
          <BeautyCard key={index} variant="subtle" style={styles.riskCard}>
            <View style={styles.riskHeader}>
              <View style={[styles.riskSeverity, { backgroundColor: getRiskColor(risk.severity) }]}>
                <Text style={styles.riskSeverityText}>{risk.severity.toUpperCase()}</Text>
              </View>
              <Text style={styles.riskType}>{risk.type}</Text>
            </View>
            
            <Text style={styles.riskDescription}>{risk.description}</Text>
            
            {risk.mitigation && (
              <View style={styles.mitigationContainer}>
                <Ionicons name="bulb" size={14} color={BeautyMinimalTheme.semantic.accent.primary} />
                <Text style={styles.mitigationText}>{risk.mitigation}</Text>
              </View>
            )}
          </BeautyCard>
        ))
      )}
    </View>
  );

  const renderProfessionalNotes = () => (
    <View style={styles.tabContent}>
      <Text style={styles.notesText}>{analysis.professionalNotes}</Text>
    </View>
  );

  const getRiskColor = (severity: string) => {
    switch (severity) {
      case 'critical': return BeautyMinimalTheme.semantic.error.primary;
      case 'high': return BeautyMinimalTheme.semantic.error.secondary;
      case 'medium': return BeautyMinimalTheme.semantic.warning.primary;
      case 'low': return BeautyMinimalTheme.semantic.accent.primary;
      default: return BeautyMinimalTheme.semantic.text.secondary;
    }
  };

  const renderActionButtons = () => {
    switch (analysis.status) {
      case ViabilityStatus.GREEN:
        return (
          <BeautyButton
            title="Proceder con Formulación"
            onPress={onProceed}
            variant="primary"
            style={styles.actionButton}
          />
        );
      case ViabilityStatus.AMBER:
        return (
          <View style={styles.buttonContainer}>
            <BeautyButton
              title="Aceptar Plan Gradual"
              onPress={onProceed}
              variant="primary"
              style={[styles.actionButton, { flex: 1 }]}
            />
            <BeautyButton
              title="Ver Alternativas"
              onPress={onViewAlternatives}
              variant="secondary"
              style={[styles.actionButton, { flex: 1, marginLeft: 8 }]}
            />
          </View>
        );
      case ViabilityStatus.RED:
        return (
          <View style={styles.buttonContainer}>
            <BeautyButton
              title="Ver Alternativas Seguras"
              onPress={onViewAlternatives}
              variant="primary"
              style={[styles.actionButton, { flex: 1 }]}
            />
            <BeautyButton
              title="Solicitar Tratamiento"
              onPress={onRequestTreatment}
              variant="secondary"
              style={[styles.actionButton, { flex: 1, marginLeft: 8 }]}
            />
          </View>
        );
    }
  };

  return (
    <BeautyCard variant="default" style={styles.container}>
      <TouchableOpacity style={styles.header} onPress={() => setIsExpanded(!isExpanded)}>
        <View style={[styles.statusIndicator, { backgroundColor: config.backgroundColor }]}>
          <Ionicons name={config.icon as any} size={24} color={config.color} />
        </View>
        
        <View style={styles.headerContent}>
          <Text style={[styles.title, { color: config.color }]}>{config.title}</Text>
          <Text style={styles.subtitle}>{config.subtitle}</Text>
        </View>
        
        <Ionicons
          name={isExpanded ? "chevron-up" : "chevron-down"}
          size={20}
          color={BeautyMinimalTheme.semantic.text.secondary}
        />
      </TouchableOpacity>

      {isExpanded && (
        <View style={styles.expandedContent}>
          <View style={styles.tabContainer}>
            <TouchableOpacity
              style={[styles.tab, activeTab === 'plan' && styles.activeTab]}
              onPress={() => setActiveTab('plan')}
            >
              <Text style={[styles.tabText, activeTab === 'plan' && styles.activeTabText]}>
                Plan
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.tab, activeTab === 'risks' && styles.activeTab]}
              onPress={() => setActiveTab('risks')}
            >
              <Text style={[styles.tabText, activeTab === 'risks' && styles.activeTabText]}>
                Riesgos ({analysis.riskFactors.length})
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.tab, activeTab === 'notes' && styles.activeTab]}
              onPress={() => setActiveTab('notes')}
            >
              <Text style={[styles.tabText, activeTab === 'notes' && styles.activeTabText]}>
                Notas
              </Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.tabContentContainer} showsVerticalScrollIndicator={false}>
            {activeTab === 'plan' && renderSessionPlan()}
            {activeTab === 'risks' && renderRiskFactors()}
            {activeTab === 'notes' && renderProfessionalNotes()}
          </ScrollView>
        </View>
      )}

      <View style={styles.actionContainer}>
        {renderActionButtons()}
      </View>
    </BeautyCard>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
  },
  statusIndicator: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  headerContent: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
  },
  subtitle: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.secondary,
    marginTop: 2,
  },
  expandedContent: {
    borderTopWidth: 1,
    borderTopColor: BeautyMinimalTheme.semantic.border.subtle,
    paddingTop: 16,
    marginTop: 8,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: BeautyMinimalTheme.semantic.background.secondary,
    borderRadius: 8,
    padding: 4,
    marginBottom: 16,
  },
  tab: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    borderRadius: 6,
  },
  activeTab: {
    backgroundColor: BeautyMinimalTheme.semantic.background.primary,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: BeautyMinimalTheme.semantic.text.secondary,
  },
  activeTabText: {
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  tabContentContainer: {
    maxHeight: 300,
  },
  tabContent: {
    gap: 12,
  },
  planHeader: {
    marginBottom: 12,
  },
  planTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  planTimeframe: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.secondary,
    marginTop: 4,
  },
  sessionCard: {
    padding: 12,
  },
  sessionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  sessionNumber: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: BeautyMinimalTheme.semantic.accent.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  sessionNumberText: {
    fontSize: 14,
    fontWeight: '600',
    color: 'white',
  },
  sessionInfo: {
    flex: 1,
  },
  sessionDescription: {
    fontSize: 14,
    fontWeight: '500',
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  sessionTime: {
    fontSize: 12,
    color: BeautyMinimalTheme.semantic.text.secondary,
    marginTop: 2,
  },
  sessionResult: {
    fontSize: 12,
    color: BeautyMinimalTheme.semantic.text.secondary,
    fontStyle: 'italic',
  },
  waitTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    gap: 4,
  },
  waitTimeText: {
    fontSize: 12,
    color: BeautyMinimalTheme.semantic.text.secondary,
  },
  noRisksText: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.secondary,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  riskCard: {
    padding: 12,
  },
  riskHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  riskSeverity: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
  },
  riskSeverityText: {
    fontSize: 10,
    fontWeight: '600',
    color: 'white',
  },
  riskType: {
    fontSize: 12,
    fontWeight: '500',
    color: BeautyMinimalTheme.semantic.text.secondary,
    textTransform: 'capitalize',
  },
  riskDescription: {
    fontSize: 14,
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: 8,
  },
  mitigationContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 6,
  },
  mitigationText: {
    flex: 1,
    fontSize: 12,
    color: BeautyMinimalTheme.semantic.text.secondary,
    lineHeight: 16,
  },
  notesText: {
    fontSize: 14,
    lineHeight: 20,
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  actionContainer: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: BeautyMinimalTheme.semantic.border.subtle,
  },
  buttonContainer: {
    flexDirection: 'row',
  },
  actionButton: {
    marginVertical: 0,
  },
});

export default SmartViabilityIndicator;
