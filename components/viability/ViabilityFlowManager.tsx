import React, { useState } from 'react';
import { SmartViabilityAnalysis } from '@/utils/viability-analyzer';
import { ViabilityStatus, AlternativeRecommendation } from '@/types/alternative-recommendations';
import AmberProcessFlow from './AmberProcessFlow';
import RedAlternativesFlow from './RedAlternativesFlow';

interface ViabilityFlowManagerProps {
  analysis: SmartViabilityAnalysis | null;
  onProceedWithPlan: (analysis: SmartViabilityAnalysis) => void;
  onSelectAlternative: (alternative: AlternativeRecommendation) => void;
  onRequestTreatment: () => void;
  onProceedWithRisk: () => void;
  onCancel: () => void;
}

export const ViabilityFlowManager: React.FC<ViabilityFlowManagerProps> = ({
  analysis,
  onProceedWithPlan,
  onSelectAlternative,
  onRequestTreatment,
  onProceedWithRisk,
  onCancel,
}) => {
  const [showAmberFlow, setShowAmberFlow] = useState(false);
  const [showRedFlow, setShowRedFlow] = useState(false);

  // Auto-show flows based on analysis status
  React.useEffect(() => {
    if (!analysis) {
      setShowAmberFlow(false);
      setShowRedFlow(false);
      return;
    }

    switch (analysis.status) {
      case ViabilityStatus.AMBER:
        setShowAmberFlow(true);
        setShowRedFlow(false);
        break;
      case ViabilityStatus.RED:
        setShowAmberFlow(false);
        setShowRedFlow(true);
        break;
      default:
        setShowAmberFlow(false);
        setShowRedFlow(false);
        break;
    }
  }, [analysis]);

  const handleAmberAcceptPlan = () => {
    if (analysis) {
      setShowAmberFlow(false);
      onProceedWithPlan(analysis);
    }
  };

  const handleAmberViewAlternatives = () => {
    setShowAmberFlow(false);
    setShowRedFlow(true);
  };

  const handleAmberCancel = () => {
    setShowAmberFlow(false);
    onCancel();
  };

  const handleRedSelectAlternative = (alternative: AlternativeRecommendation) => {
    setShowRedFlow(false);
    onSelectAlternative(alternative);
  };

  const handleRedRequestTreatment = () => {
    setShowRedFlow(false);
    onRequestTreatment();
  };

  const handleRedProceedWithRisk = () => {
    setShowRedFlow(false);
    onProceedWithRisk();
  };

  const handleRedCancel = () => {
    setShowRedFlow(false);
    onCancel();
  };

  if (!analysis) return null;

  return (
    <>
      {/* Amber Process Flow */}
      <AmberProcessFlow
        visible={showAmberFlow}
        analysis={analysis}
        onAcceptPlan={handleAmberAcceptPlan}
        onViewAlternatives={handleAmberViewAlternatives}
        onCancel={handleAmberCancel}
      />

      {/* Red Alternatives Flow */}
      <RedAlternativesFlow
        visible={showRedFlow}
        analysis={analysis}
        onSelectAlternative={handleRedSelectAlternative}
        onRequestTreatment={handleRedRequestTreatment}
        onProceedWithRisk={handleRedProceedWithRisk}
        onCancel={handleRedCancel}
      />
    </>
  );
};

/**
 * Hook para manejar los flujos de viabilidad
 */
export const useViabilityFlows = () => {
  const [currentAnalysis, setCurrentAnalysis] = useState<SmartViabilityAnalysis | null>(null);

  const showFlow = (analysis: SmartViabilityAnalysis) => {
    setCurrentAnalysis(analysis);
  };

  const hideFlow = () => {
    setCurrentAnalysis(null);
  };

  const isFlowVisible = currentAnalysis !== null;

  return {
    currentAnalysis,
    showFlow,
    hideFlow,
    isFlowVisible,
  };
};

export default ViabilityFlowManager;
