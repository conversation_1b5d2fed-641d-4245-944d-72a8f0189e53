# Testing Guide: Brand Dossiers Integration

## 🧪 Pruebas de Integración

### Componentes a Probar

1. **BrandDossierCard** - Tarjeta principal de información de marca
2. **BrandQuickInfo** - Información rápida cuando está colapsado
3. **useBrandDossiers** - Hook para acceder a los dossiers
4. **FormulationStep** - Integración en el flujo principal

### Casos de Prueba

#### 1. Visualización de Dossiers

**Marcas con Dossiers Completos:**
- ✅ Wella Professionals + Koleston Perfect
- ✅ Wella Professionals + Illumina Color
- ✅ Wella Professionals + Color Touch
- ✅ Schwarzkopf Professional + IGORA ROYAL
- ✅ L'Oréal Professionnel + Majirel
- ✅ Salerm Cosmetics + Salermvison
- ✅ Arkhé Cosmetics + Color Pure

**Marcas sin Dossiers:**
- ❌ Redken (debería no mostrar la tarjeta)
- ❌ Matrix (debería no mostrar la tarjeta)

#### 2. Estados de la Tarjeta

**Estado Cola<PERSON>ado:**
- Muestra nombre de marca y línea
- Badge "Experto" visible
- BrandQuickInfo con información esencial:
  - Ratio de mezcla
  - Tiempo de procesamiento
  - Volumen máximo de oxidante
  - Tipo de producto
  - Nivel de cobertura
  - Poder de aclaración

**Estado Expandido:**
- Tabs "Marca" y "Línea" (si ambos dossiers están disponibles)
- Información detallada de marca:
  - Sistema de numeración
  - Características especiales
  - Notas profesionales
  - Tips de experto
- Información detallada de línea:
  - Especificaciones técnicas
  - Casos de uso ideales
  - Consejos de neutralización
  - Consejos de aplicación

#### 3. Interacciones

**Expansión/Colapso:**
- Tap en header para expandir/colapsar
- Icono chevron cambia dirección
- Animación suave de transición

**Navegación entre Tabs:**
- Tab "Marca" muestra información de marca
- Tab "Línea" muestra información de línea
- Solo un tab activo a la vez

#### 4. Integración en FormulationStep

**Condiciones de Visualización:**
- Solo aparece cuando hay marca Y línea seleccionadas
- Solo aparece cuando existe dossier para esa combinación
- Se posiciona después de la selección de marca/línea
- Antes de la sección de conversión de marca

**Comportamiento:**
- Se actualiza automáticamente al cambiar marca/línea
- Mantiene estado expandido/colapsado independiente
- No interfiere con otros componentes

### Datos de Prueba

#### Wella Professionals + Koleston Perfect
```
Información Rápida:
- Mezcla: 1:1
- Tiempo: 35-45 min
- Max Vol: 40vol
- Tipo: permanent
- Cobertura: maximum
- Aclaración: 3 niveles

Información Detallada:
- Sistema: Level/Tone (e.g., 7/1, 10/81)
- Características: ME+ technology, Metal Purifier, Microlight technology
- Notas: "La línea más robusta de Wella..."
- Tips: "Usar siempre Wellaplex en procesos de aclaración"
```

#### L'Oréal Professionnel + Majirel
```
Información Rápida:
- Mezcla: 1:1.5
- Tiempo: 35 min
- Max Vol: 40vol
- Tipo: permanent
- Cobertura: full
- Aclaración: 3 niveles

Información Detallada:
- Sistema: Point system (e.g., 8.3, 10.21)
- Características: Ionène G + Incell, Oil Delivery System
- Notas: "L'Oréal es pionero en innovación científica..."
- Tips: "Usar Smartbond en procesos de aclaración"
```

### Flujo de Prueba Manual

1. **Abrir FormulationStep**
2. **Seleccionar marca sin dossier** (ej: Redken)
   - ✅ No debe aparecer BrandDossierCard
3. **Seleccionar marca con dossier** (ej: Wella Professionals)
4. **Seleccionar línea con dossier** (ej: Koleston Perfect)
   - ✅ Debe aparecer BrandDossierCard colapsado
   - ✅ Debe mostrar BrandQuickInfo con datos correctos
5. **Tap en header para expandir**
   - ✅ Debe mostrar tabs "Marca" y "Línea"
   - ✅ Tab "Marca" debe estar activo por defecto
   - ✅ Debe mostrar información detallada de marca
6. **Tap en tab "Línea"**
   - ✅ Debe cambiar a información de línea
   - ✅ Debe mostrar datos específicos de Koleston Perfect
7. **Tap en header para colapsar**
   - ✅ Debe volver a mostrar solo BrandQuickInfo
8. **Cambiar a otra marca/línea**
   - ✅ Debe actualizar información automáticamente
   - ✅ Debe mantener estado colapsado

### Verificación de Datos

#### Hook useBrandDossiers
```typescript
const { getBrandDossier, getLineDossier, hasDossierData } = useBrandDossiers();

// Pruebas
console.log(getBrandDossier('Wella Professionals')); // Debe retornar objeto
console.log(getLineDossier('Wella Professionals', 'Koleston Perfect')); // Debe retornar objeto
console.log(hasDossierData('Wella Professionals', 'Koleston Perfect')); // Debe retornar true
console.log(hasDossierData('Redken', 'Color Gels')); // Debe retornar false
```

#### Normalización de Nombres
```typescript
// Debe funcionar con diferentes formatos
getBrandDossier('wella professionals'); // ✅
getBrandDossier('Wella Professionals'); // ✅
getBrandDossier('WELLA PROFESSIONALS'); // ✅
getLineDossier('wella professionals', 'koleston perfect'); // ✅
```

### Problemas Conocidos y Soluciones

#### 1. Nombres de Marca Inconsistentes
**Problema:** Los nombres en brands-data.ts pueden no coincidir exactamente
**Solución:** Normalización en useBrandDossiers con toLowerCase() y trim()

#### 2. Dossiers Incompletos
**Problema:** Algunas marcas pueden tener solo dossier de marca o solo de línea
**Solución:** Verificación condicional en BrandDossierCard

#### 3. Performance
**Problema:** Re-renderizado innecesario al cambiar marca
**Solución:** useMemo en hook para cachear resultados

### Métricas de Éxito

- ✅ 100% de marcas principales tienen dossiers
- ✅ Información se muestra correctamente en todos los estados
- ✅ No hay errores de renderizado
- ✅ Transiciones suaves entre estados
- ✅ Datos técnicos son precisos y útiles
- ✅ Integración no interfiere con flujo existente

### Próximas Mejoras

1. **Animaciones:** Transiciones más suaves para expand/collapse
2. **Favoritos:** Marcar información más útil como favorita
3. **Búsqueda:** Buscar dentro de la información de dossier
4. **Comparación:** Comparar especificaciones entre marcas
5. **Actualización:** Sistema para actualizar dossiers remotamente

---

**Resultado Esperado:** Los estilistas ahora tienen acceso inmediato a información técnica experta sobre cada marca y línea, mejorando significativamente la precisión y confianza en sus formulaciones.
