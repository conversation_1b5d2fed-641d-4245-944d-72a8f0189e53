# Guía del Guardián de Colorimetría

## 🛡️ Introducción

El **Guardián de Colorimetría** es un sistema proactivo de validación que detecta violaciones críticas de las reglas de colorimetría ANTES de generar fórmulas. Convierte errores técnicos en conversaciones guiadas y profesionales.

## 🎯 Objetivos

1. **Seguridad Primero**: Prevenir formulaciones peligrosas
2. **Educación Continua**: Explicar el "por qué" detrás de cada regla
3. **Experiencia Conversacional**: Diálogos profesionales en lugar de errores técnicos
4. **Confianza del Usuario**: Transparencia en las decisiones de la IA

## 🏗️ Arquitectura del Sistema

### Componentes Principales

1. **`colorimetry-rules.ts`** - Reglas centralizadas y sagradas
2. **`ColorimetryGuardian`** - Motor de validación proactiva
3. **`ColorimetryGuardianDialog`** - UI conversacional
4. **`useColorimetryGuardian`** - Hook para integración fácil
5. **`GuardianStatusIndicator`** - Indicador visual de estado

### Flujo de Validación

```
Análisis de Color Deseado
         ↓
Guardián de Colorimetría (Pre-validación)
         ↓
¿Violaciones Detectadas?
    ↙        ↘
   SÍ        NO
    ↓         ↓
Diálogo    Continuar
Guiado    a Formulación
```

## 📋 Las Reglas Sagradas

### 1. **Tinte No Aclara Tinte** (CRÍTICA)
- **Qué**: El color artificial no puede ser aclarado con tinte
- **Por qué**: El pigmento artificial requiere extracción química específica
- **Acción**: Proceso de extracción + coloración en 2 sesiones

### 2. **Neutralización del Fondo de Aclaración** (ADVERTENCIA)
- **Qué**: Aclarar revela pigmentos subyacentes que deben neutralizarse
- **Por qué**: Sin neutralización, aparecen reflejos no deseados
- **Acción**: Incluir matices neutralizadores en la fórmula

### 3. **Volumen de Oxidante Seguro** (ADVERTENCIA)
- **Qué**: El volumen debe ser apropiado para el nivel de aclaración
- **Por qué**: Más volumen del necesario daña; menos no logra el resultado
- **Acción**: Usar tabla de volúmenes según niveles de aclaración

### 4. **Cobertura de Canas** (ADVERTENCIA)
- **Qué**: Las canas requieren fórmulas específicas
- **Por qué**: Las canas son resistentes por falta de melanina
- **Acción**: Incluir base natural en la mezcla

### 5. **Sales Metálicas** (CRÍTICA)
- **Qué**: Incompatibilidad peligrosa con productos profesionales
- **Por qué**: Pueden causar reacciones químicas violentas
- **Acción**: Test de mechón obligatorio + tratamiento de eliminación

### 6. **Compatibilidad con Henna** (CRÍTICA)
- **Qué**: La henna puede interferir con productos químicos
- **Por qué**: Forma una barrera que afecta la penetración del color
- **Acción**: Test de mechón extensivo + productos específicos

## 🔧 Uso del Sistema

### Integración Básica

```typescript
import { useColorimetryGuardian, buildValidationRequest } from '@/src/service/hooks/useColorimetryGuardian';

const MyComponent = () => {
  const guardian = useColorimetryGuardian({
    onProceed: () => {
      // Usuario decidió continuar a pesar de advertencias
      proceedToFormulation();
    },
    onCancel: () => {
      // Usuario decidió revisar el diagnóstico
      goBackToDiagnosis();
    },
    onAlternativeSelected: (approach) => {
      // Usuario seleccionó una alternativa
      implementAlternative(approach);
    }
  });

  const validateProcess = async () => {
    const request = buildValidationRequest(serviceData, desiredResult);
    await guardian.validateColorProcess(request);
  };

  return (
    <View>
      <GuardianStatusIndicator 
        validationResult={guardian.validationResult}
        onPress={guardian.showGuardianDialog}
      />
      
      <ColorimetryGuardianDialog
        visible={guardian.showDialog}
        validationResult={guardian.validationResult}
        onProceed={guardian.handleProceed}
        onCancel={guardian.handleCancel}
        onSelectAlternative={guardian.handleAlternativeSelected}
      />
    </View>
  );
};
```

### Validación Manual

```typescript
import { ColorimetryGuardian } from '@/supabase/functions/salonier-assistant/utils/formula-validator';

const request = {
  currentLevel: 4,
  desiredLevel: 8,
  currentState: 'colored',
  hairCondition: 'healthy',
  // ... otros campos
};

const result = ColorimetryGuardian.validateBeforeFormulation(request);

if (result.violations.length > 0) {
  console.log('Violaciones detectadas:', result.violations);
  console.log('Enfoque recomendado:', result.recommendedApproach);
  console.log('Sesiones estimadas:', result.estimatedSessions);
}
```

## 🎨 Estados del Guardián

### 🟢 **Proceso Seguro**
- Sin violaciones detectadas
- Viable en una sola sesión
- Riesgo bajo

### 🟡 **Precaución Profesional**
- Advertencias menores
- Puede requerir múltiples sesiones
- Riesgo medio

### 🔴 **Riesgo Crítico**
- Violaciones críticas detectadas
- No recomendado proceder
- Riesgo alto/crítico

## 📊 Métricas y Logging

El sistema registra automáticamente:
- Violaciones detectadas por tipo
- Decisiones del usuario (proceder/cancelar/alternativa)
- Tiempo de validación
- Patrones de uso

## 🔄 Flujo de Integración

### En DesiredColorStep

1. **Análisis de Color Deseado** → Ejecuta automáticamente el Guardián
2. **Violaciones Detectadas** → Muestra diálogo conversacional
3. **Usuario Decide** → Proceder, cancelar o seleccionar alternativa
4. **Logging** → Registra decisión para aprendizaje

### En FormulationStep

1. **Pre-formulación** → Validación adicional con marca específica
2. **Durante Formulación** → Explicaciones en tiempo real
3. **Post-formulación** → Validación final de la fórmula generada

## 🚀 Beneficios

### Para el Estilista
- **Confianza**: Sabe que la app no le dará consejos peligrosos
- **Educación**: Aprende el "por qué" de cada regla
- **Eficiencia**: Detecta problemas antes de perder tiempo

### Para el Salón
- **Seguridad**: Reduce riesgos de daño capilar
- **Reputación**: Evita resultados impredecibles
- **Eficiencia**: Menos servicios fallidos

### Para Salonier
- **Diferenciación**: Sistema único en el mercado
- **Confianza del Usuario**: Transparencia en decisiones de IA
- **Aprendizaje Continuo**: Datos para mejorar el sistema

## 🔮 Próximas Mejoras

1. **Aprendizaje Automático**: Refinar reglas basado en feedback
2. **Reglas Específicas de Marca**: Validaciones por marca/línea
3. **Integración con Historial**: Usar experiencia previa del cliente
4. **Predicción Visual**: Mostrar resultado esperado vs. riesgos
5. **Alertas Contextuales**: Notificaciones basadas en condiciones ambientales

## 📝 Mantenimiento

### Agregar Nueva Regla

1. Definir en `SACRED_COLORIMETRY_RULES`
2. Implementar validator function
3. Agregar explicaciones conversacionales
4. Actualizar tests
5. Documentar en esta guía

### Modificar Regla Existente

1. Actualizar en `colorimetry-rules.ts`
2. Verificar impacto en `ColorimetryGuardian`
3. Actualizar tests
4. Comunicar cambios al equipo

---

**Recuerda**: El Guardián de Colorimetría es el primer paso hacia una IA verdaderamente confiable y educativa en colorimetría profesional. Su objetivo no es limitar, sino empoderar al estilista con conocimiento y seguridad.
