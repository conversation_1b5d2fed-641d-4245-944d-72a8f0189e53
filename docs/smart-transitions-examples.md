# Ejemplos de Transiciones Inteligentes

## 🚦 Casos de Uso del Sistema

### Caso 1: Diagnóstico Incompleto → Color Deseado

**Situación**: Usuario intenta avanzar sin completar el diagnóstico

```typescript
// Estado del servicio
serviceData = {
  overallTone: null, // ❌ Faltante
  zoneColorAnalysis: {
    roots: { level: 5 },
    mids: null, // ❌ Faltante
    ends: null  // ❌ Faltante
  }
}

// Resultado de la validación
validation = {
  status: TransitionStatus.BLOCKED,
  title: "Diagnóstico Incompleto",
  message: "Completa el diagnóstico antes de continuar",
  details: [
    "Diagnóstico de color incompleto",
    "Análisis incompleto en 2 zona(s)"
  ],
  recommendations: [
    {
      id: "complete_diagnosis",
      type: "action",
      title: "Completar Diagnóstico",
      description: "Especifica el tono y reflejo base del cabello"
    }
  ],
  canProceed: false
}
```

**UI Resultante**:
- 🔴 Diálogo de bloqueo
- Botón único: "Entendido" (vuelve al diagnóstico)
- Lista de elementos faltantes
- Recomendaciones de acción

---

### Caso 2: Cabello Dañado → Color Deseado

**Situación**: Diagnóstico completo pero cabello muy dañado

```typescript
// Estado del servicio
serviceData = {
  overallTone: "Castaño",
  zonePhysicalAnalysis: {
    roots: { damage: "Medio" },
    mids: { damage: "Alto" }, // ⚠️ Daño severo
    ends: { damage: "Alto" }  // ⚠️ Daño severo
  }
}

// Resultado de la validación
validation = {
  status: TransitionStatus.WARNING,
  title: "Cabello Dañado Detectado",
  message: "El cabello presenta daño severo. Considera opciones más suaves.",
  recommendations: [
    {
      id: "damage_warning",
      type: "warning",
      title: "Cabello Muy Dañado Detectado",
      description: "Considera opciones de coloración más suaves o tratamiento previo"
    }
  ],
  riskLevel: "high",
  canProceed: true
}
```

**UI Resultante**:
- ⚠️ Diálogo de advertencia
- Botones: "Entendido, Continuar" + "Revisar"
- Advertencia sobre el daño
- Recomendación de precaución

---

### Caso 3: Proceso Gradual Requerido → Formulación

**Situación**: Color deseado requiere múltiples sesiones

```typescript
// Análisis de viabilidad
smartViabilityAnalysis = {
  status: ViabilityStatus.AMBER,
  sessionPlan: {
    totalSessions: 3,
    estimatedTimeframe: "6-8 semanas",
    sessionDetails: [
      {
        sessionNumber: 1,
        description: "Decapado suave para eliminar color artificial",
        estimatedTime: 90,
        waitTime: 7,
        expectedResult: "Color artificial eliminado"
      },
      {
        sessionNumber: 2,
        description: "Aclaración gradual - Primera sesión",
        estimatedTime: 150,
        waitTime: 14,
        expectedResult: "Nivel 8-9 alcanzado"
      },
      {
        sessionNumber: 3,
        description: "Aplicación de color final y tonalización",
        estimatedTime: 120,
        expectedResult: "Color final nivel 10 logrado"
      }
    ]
  }
}

// Resultado de la validación
validation = {
  status: TransitionStatus.CONFIRMATION,
  title: "Proceso Gradual Requerido",
  message: "Este cambio requerirá 3 sesiones. ¿Continuar con plan gradual?",
  details: [
    "Sesión 1: Decapado suave para eliminar color artificial",
    "Sesión 2: Aclaración gradual - Primera sesión", 
    "Sesión 3: Aplicación de color final y tonalización"
  ],
  recommendations: [
    {
      id: "accept_gradual",
      type: "action",
      title: "Aceptar Proceso Gradual",
      description: "3 sesiones durante 6-8 semanas"
    },
    {
      id: "view_alternatives",
      type: "alternative",
      title: "Ver Alternativas",
      description: "Explorar opciones más directas"
    }
  ],
  requiresConfirmation: true
}
```

**UI Resultante**:
- 🟡 Diálogo de confirmación
- Botones: "Sí, Continuar" + "No, Revisar"
- Plan detallado de 3 sesiones
- Opciones de alternativas

---

### Caso 4: Proceso No Recomendado → Formulación

**Situación**: Cambio de color con riesgo crítico

```typescript
// Análisis de viabilidad
smartViabilityAnalysis = {
  status: ViabilityStatus.RED,
  riskFactors: [
    {
      type: "chemical",
      severity: "critical",
      description: "Sales metálicas detectadas - Riesgo de reacción química",
      mitigation: "Test de mechón obligatorio antes de proceder"
    },
    {
      type: "physical", 
      severity: "high",
      description: "Daño severo en medios y puntas",
      mitigation: "Tratamiento reconstructivo previo recomendado"
    }
  ],
  alternatives: [
    {
      targetColor: "Nivel 7",
      reason: "Aclaración gradual más segura",
      viabilityImprovement: ViabilityStatus.AMBER,
      description: "Primer paso hacia el objetivo final"
    },
    {
      targetColor: "Mechas balayage",
      reason: "Efecto sin comprometer todo el cabello", 
      viabilityImprovement: ViabilityStatus.GREEN,
      description: "Luminosidad sin daño generalizado"
    }
  ]
}

// Resultado de la validación
validation = {
  status: TransitionStatus.BLOCKED,
  title: "Proceso No Recomendado",
  message: "Riesgo significativo de daño capilar. Considera alternativas más seguras.",
  details: [
    "Sales metálicas detectadas - Riesgo de reacción química",
    "Daño severo en medios y puntas"
  ],
  recommendations: [
    {
      id: "alt_nivel_7",
      type: "alternative",
      title: "Nivel 7",
      description: "Primer paso hacia el objetivo final"
    },
    {
      id: "alt_mechas_balayage", 
      type: "alternative",
      title: "Mechas balayage",
      description: "Luminosidad sin daño generalizado"
    }
  ],
  riskLevel: "critical",
  canProceed: false
}
```

**UI Resultante**:
- 🔴 Diálogo de bloqueo crítico
- Botón único: "Entendido"
- Lista de riesgos críticos
- Alternativas seguras disponibles

---

### Caso 5: Formulación Incompleta → Finalización

**Situación**: Intento de finalizar sin fórmula completa

```typescript
// Estado del servicio
serviceData = {
  formula: "", // ❌ Faltante
  selectedBrand: "Wella Professionals",
  selectedLine: "", // ❌ Faltante
  stockValidation: {
    checked: true,
    hasStock: false,
    missingProducts: ["Illumina Color 10/36", "Welloxon Perfect 30 Vol"]
  }
}

// Resultado de la validación
validation = {
  status: TransitionStatus.BLOCKED,
  title: "Formulación Incompleta",
  message: "Completa la formulación antes de proceder",
  details: [
    "Fórmula no generada",
    "Marca y línea no seleccionadas"
  ],
  recommendations: [
    {
      id: "check_stock",
      type: "warning", 
      title: "Productos Faltantes",
      description: "Faltan 2 productos en inventario"
    }
  ],
  canProceed: false
}
```

**UI Resultante**:
- 🔴 Diálogo de bloqueo
- Lista de elementos faltantes
- Advertencia sobre stock
- Botón: "Entendido" (vuelve a formulación)

---

## 🎯 Flujo de Usuario Completo

### Escenario: Cabello Nivel 4 → Rubio Platino Nivel 10

1. **Diagnóstico → Color Deseado**
   - ✅ Diagnóstico completo
   - ⚠️ Advertencia: "Cabello previamente teñido detectado"
   - Usuario: "Entendido, Continuar"

2. **Color Deseado → Formulación**
   - 🟡 Confirmación: "Este cambio requerirá 3 sesiones. ¿Continuar?"
   - Plan mostrado: Decapado → Aclaración → Color final
   - Usuario: "Sí, Continuar"

3. **Formulación → Finalización**
   - ✅ Fórmula generada para Sesión 1
   - ⚠️ Advertencia: "Proceso de alto riesgo - Documentar consentimiento"
   - Usuario: "Entendido, Continuar"

4. **Resultado**
   - Servicio completado con plan de 3 sesiones
   - Próxima cita programada automáticamente
   - Cliente informado del proceso gradual

---

## 🔧 Configuración de Validaciones

### Personalización por Salón

```typescript
// Configuración de sensibilidad
const transitionConfig = {
  riskTolerance: 'conservative', // 'conservative' | 'moderate' | 'aggressive'
  requireConfirmation: {
    multipleSessions: true,
    highRisk: true,
    stockIssues: false
  },
  autoBlock: {
    criticalRisks: true,
    incompleteData: true,
    metallic: true
  }
};
```

### Métricas de Adopción

```typescript
// Tracking de decisiones
const transitionMetrics = {
  blockedTransitions: 15,
  warningsProceed: 8,
  warningsCanceled: 3,
  confirmationsProceed: 12,
  confirmationsCanceled: 2,
  alternativesAccepted: 6
};
```

---

**Resultado**: Las Transiciones Inteligentes proporcionan una experiencia guiada que previene errores, educa al usuario y mejora la calidad del servicio mediante validaciones contextuales proactivas.
