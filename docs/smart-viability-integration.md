# Integración del Semáforo Inteligente de Viabilidad

## 🚦 Sistema de Estados

### Estados del Semáforo

#### 🟢 VERDE - Proceso Directo
- **Criterio**: Cabello saludable, cambio viable en 1 sesión
- **Acción**: Proceder directamente a formulación
- **UI**: Botón verde "Proceder con Formulación"
- **Mensaje**: "Coloración segura en una sesión"

#### 🟡 ÁMBAR - Proceso Gradual  
- **Criterio**: Requiere múltiples sesiones pero es viable
- **Acción**: Mostrar plan detallado de sesiones
- **UI**: <PERSON>t<PERSON> amarillo "Aceptar Plan Gradual" + "Ver Alternativas"
- **Mensaje**: "X sesiones recomendadas durante Y tiempo"

#### 🔴 ROJO - No Recomendado
- **Criterio**: Riesgo significativo de daño capilar
- **Acción**: Mostrar alternativas seguras
- **UI**: Botón rojo "Ver Alternativas Seguras" + "Solicitar Tratamiento"
- **Mensaje**: "Riesgo significativo de daño"

## 🏗️ Arquitectura de Integración

### Componentes Principales

1. **SmartViabilityIndicator** - Componente visual principal
2. **useSmartViability** - Hook para lógica de negocio
3. **analyzeSmartViability** - Función de análisis central
4. **ViabilityStatus** - Enum de estados

### Flujo de Integración

```
DesiredColorStep
    ↓
Análisis de Color Deseado
    ↓
analyzeSmartViability()
    ↓
SmartViabilityIndicator
    ↓
Acciones Contextuales
```

## 📋 Plan de Sesiones Detallado

### Ejemplo: ÁMBAR - Cabello Teñido a Rubio Platino

```typescript
sessionPlan: {
  totalSessions: 3,
  estimatedTimeframe: "6-8 semanas",
  sessionDetails: [
    {
      sessionNumber: 1,
      process: [ProcessType.COLOR_REMOVAL],
      description: "Decapado suave para eliminar color artificial",
      estimatedTime: 90,
      waitTime: 7,
      expectedResult: "Color artificial eliminado, cabello preparado"
    },
    {
      sessionNumber: 2,
      process: [ProcessType.BLEACHING],
      description: "Aclaración gradual - Primera sesión",
      estimatedTime: 150,
      waitTime: 14,
      expectedResult: "Nivel 8-9 alcanzado"
    },
    {
      sessionNumber: 3,
      process: [ProcessType.DIRECT_COLOR, ProcessType.TONING],
      description: "Aplicación de color final y tonalización",
      estimatedTime: 120,
      expectedResult: "Color final nivel 10 logrado"
    }
  ]
}
```

## ⚠️ Sistema de Factores de Riesgo

### Tipos de Riesgo

#### Químicos
- **Sales metálicas**: Crítico - Test obligatorio
- **Henna**: Alto - Solo oscurecer
- **Incompatibilidades**: Medio - Evaluar productos

#### Físicos
- **Daño severo**: Alto - Tratamiento previo
- **Daño moderado**: Medio - Protectores
- **Porosidad extrema**: Alto - Relleno previo

#### Técnicos
- **Múltiples procesos**: Medio - Planificar gradual
- **Aclaración extrema**: Alto - Evaluar viabilidad
- **Cambio drástico**: Medio - Considerar alternativas

#### Cliente
- **Expectativas irreales**: Medio - Educación
- **Historial de reacciones**: Alto - Tests previos
- **Mantenimiento limitado**: Bajo - Ajustar plan

### Severidad de Riesgos

```typescript
'critical' → ROJO automático
'high' → ROJO si ≥2, ÁMBAR si 1
'medium' → ÁMBAR si ≥2, VERDE si 1
'low' → No afecta estado
```

## 🎯 Alternativas Inteligentes

### Cuando el Estado es ROJO

#### Aclaración Extrema (Nivel 4 → 10)
```typescript
alternatives: [
  {
    targetColor: "Nivel 7",
    reason: "Aclaración gradual más segura",
    viabilityImprovement: ViabilityStatus.AMBER,
    description: "Primer paso hacia el objetivo final"
  },
  {
    targetColor: "Mechas balayage",
    reason: "Efecto sin comprometer todo el cabello",
    viabilityImprovement: ViabilityStatus.GREEN,
    description: "Luminosidad sin daño generalizado"
  }
]
```

#### Cabello Muy Dañado
```typescript
alternatives: [
  {
    targetColor: "Tratamiento + color suave",
    reason: "Priorizar salud capilar",
    viabilityImprovement: ViabilityStatus.AMBER,
    description: "Recuperación seguida de coloración"
  }
]
```

## 💡 Notas Profesionales Contextuales

### Ejemplos por Estado

#### VERDE
```
"✅ PROCESO DIRECTO: Cabello en condiciones óptimas para coloración en una sesión. 
Procede con confianza siguiendo protocolos estándar de la marca seleccionada."
```

#### ÁMBAR
```
"⚠️ PROCESO GRADUAL: Requiere planificación de múltiples sesiones para resultado óptimo. 
Se recomienda 3 sesiones con descansos de 1-2 semanas entre cada una. 
Evaluar resultado en cada sesión antes de proceder."
```

#### ROJO
```
"🚫 PROCESO NO RECOMENDADO: Riesgo significativo de daño capilar. 
Considera alternativas más seguras o tratamiento reconstructivo previo. 
Si el cliente insiste, documenta riesgos y obtén consentimiento informado.

🔴 RIESGOS CRÍTICOS:
• Sales metálicas detectadas: Test de mechón obligatorio y posible tratamiento de eliminación
• Daño severo en medios: Tratamiento reconstructivo previo recomendado"
```

## 🔧 Implementación en Componentes

### DesiredColorStep Integration

```typescript
import { useSmartViability } from '@/hooks/useSmartViability';
import SmartViabilityIndicator from '@/components/viability/SmartViabilityIndicator';

const DesiredColorStep = ({ data, onUpdate, onNext }) => {
  const {
    analysis,
    isAnalyzing,
    canProceedDirectly,
    requiresMultipleSessions,
    isNotRecommended
  } = useSmartViability({
    analysisResult: data.analysisResult,
    desiredAnalysisResult: data.desiredAnalysisResult,
    zoneColorAnalysis: data.zoneColorAnalysis,
    autoAnalyze: true
  });

  const handleProceed = () => {
    if (canProceedDirectly) {
      onNext(); // Ir directo a formulación
    } else if (requiresMultipleSessions) {
      // Mostrar plan de sesiones y confirmar
      showSessionPlanDialog();
    }
  };

  const handleViewAlternatives = () => {
    // Mostrar modal con alternativas
    setShowAlternativesModal(true);
  };

  return (
    <View>
      {/* Análisis de color deseado */}
      
      {analysis && (
        <SmartViabilityIndicator
          analysis={analysis}
          onProceed={handleProceed}
          onViewAlternatives={handleViewAlternatives}
          onRequestTreatment={handleRequestTreatment}
        />
      )}
      
      {/* Botones de navegación condicionales */}
      {canProceedDirectly && (
        <BeautyButton title="Continuar" onPress={onNext} />
      )}
    </View>
  );
};
```

### FormulationStep Integration

```typescript
const FormulationStep = ({ data }) => {
  const { analysis } = useSmartViability({
    analysisResult: data.analysisResult,
    desiredAnalysisResult: data.desiredAnalysisResult,
    zoneColorAnalysis: data.zoneColorAnalysis,
    autoAnalyze: false // Ya analizado en paso anterior
  });

  // Mostrar información del plan si es ÁMBAR
  if (analysis?.status === ViabilityStatus.AMBER) {
    return (
      <View>
        <SessionPlanSummary plan={analysis.sessionPlan} />
        <CurrentSessionIndicator sessionNumber={1} />
        {/* Formulación para la sesión actual */}
      </View>
    );
  }

  // Formulación normal para VERDE
  return <StandardFormulationUI />;
};
```

## 📊 Métricas y Seguimiento

### KPIs del Semáforo

- **Distribución de Estados**: % Verde/Ámbar/Rojo
- **Precisión de Predicciones**: Resultados vs. predicciones
- **Adopción de Alternativas**: % que acepta sugerencias
- **Satisfacción por Estado**: Rating por tipo de proceso

### Datos para Mejora Continua

- Casos donde VERDE resultó problemático
- Alternativas más aceptadas en ROJO
- Tiempo real vs. estimado en ÁMBAR
- Factores de riesgo más frecuentes

## 🚀 Beneficios del Sistema

### Para el Estilista
- **Claridad**: Estados inequívocos para tomar decisiones
- **Confianza**: Respaldo científico en cada recomendación
- **Eficiencia**: Planes detallados reducen incertidumbre
- **Seguridad**: Prevención proactiva de daños

### Para el Cliente
- **Transparencia**: Comprende el proceso y tiempos
- **Expectativas**: Resultados realistas desde el inicio
- **Opciones**: Alternativas cuando el ideal no es viable
- **Confianza**: Proceso profesional y bien planificado

### Para el Salón
- **Calidad**: Menos errores y mejores resultados
- **Eficiencia**: Planificación optimizada de citas
- **Diferenciación**: Servicio más profesional
- **Rentabilidad**: Procesos graduales = más servicios

---

**Resultado**: El Semáforo Inteligente transforma la toma de decisiones de reactiva a proactiva, proporcionando claridad, seguridad y profesionalismo en cada servicio de coloración.
