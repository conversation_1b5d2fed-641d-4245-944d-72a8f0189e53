import React, { useState, useCallback, useMemo } from 'react';
import { SmartViabilityAnalysis } from '@/utils/viability-analyzer';
import {
  AlternativeRecommendation,
  ViabilityStatus,
  AlternativeGenerationContext,
  AlternativeCategory
} from '@/types/alternative-recommendations';
import {
  generateAlternativeRecommendations,
  getBestAlternatives,
  filterAlternativesByViability
} from '@/utils/alternative-recommendations';
import { DesiredColorAnalysisResult } from '@/types/desired-analysis';
import { HairZone, ZoneColorAnalysis } from '@/types/hair-diagnosis';

export interface UseAlternativeRecommendationsProps {
  analysis?: SmartViabilityAnalysis;
  desiredColorAnalysis?: DesiredColorAnalysisResult;
  zoneAnalysis?: Record<HairZone, Partial<ZoneColorAnalysis>>;
  autoGenerate?: boolean;
}

export interface AlternativeRecommendationsState {
  categories: AlternativeCategory[];
  bestAlternatives: AlternativeRecommendation[];
  safeAlternatives: AlternativeRecommendation[];
  isLoading: boolean;
  error: string | null;
}

export const useAlternativeRecommendations = ({
  analysis,
  desiredColorAnalysis,
  zoneAnalysis,
  autoGenerate = true
}: UseAlternativeRecommendationsProps = {}) => {
  const [state, setState] = useState<AlternativeRecommendationsState>({
    categories: [],
    bestAlternatives: [],
    safeAlternatives: [],
    isLoading: false,
    error: null
  });

  const [selectedAlternative, setSelectedAlternative] = useState<AlternativeRecommendation | null>(null);

  /**
   * Genera el contexto para las alternativas basado en el análisis actual
   */
  const generateContext = useCallback((): AlternativeGenerationContext | null => {
    if (!analysis || !desiredColorAnalysis) return null;

    // Extraer información del análisis
    const currentLevel = analysis.sessionPlan?.sessionDetails?.[0]?.estimatedTime ? 
      parseInt(analysis.sessionPlan.sessionDetails[0].description.match(/\d+/)?.[0] || '5') : 5;
    
    const desiredLevel = parseInt(desiredColorAnalysis.general?.overallLevel?.match(/\d+/)?.[0] || '7');

    return {
      currentLevel,
      desiredLevel,
      currentTone: 'natural', // Simplificado - podría extraerse del diagnóstico
      desiredTone: desiredColorAnalysis.general?.overallTone || 'natural',
      hairCondition: determineHairConditionFromAnalysis(analysis),
      technique: 'full_color', // Simplificado - podría extraerse del análisis
      riskFactors: analysis.riskFactors.map(rf => rf.description),
      zoneAnalysis: zoneAnalysis || {}
    };
  }, [analysis, desiredColorAnalysis, zoneAnalysis]);

  /**
   * Genera recomendaciones alternativas
   */
  const generateRecommendations = useCallback(async () => {
    const context = generateContext();
    if (!context) {
      setState(prev => ({ ...prev, error: 'Contexto insuficiente para generar alternativas' }));
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Generar categorías de alternativas
      const categories = generateAlternativeRecommendations(context);
      
      // Obtener las mejores alternativas
      const bestAlternatives = getBestAlternatives(categories);
      
      // Filtrar solo alternativas seguras (GREEN y AMBER)
      const safeCategories = filterAlternativesByViability(categories, ViabilityStatus.AMBER);
      const safeAlternatives = getBestAlternatives(safeCategories);

      setState(prev => ({
        ...prev,
        categories,
        bestAlternatives,
        safeAlternatives,
        isLoading: false
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Error generando alternativas',
        isLoading: false
      }));
    }
  }, [generateContext]);

  /**
   * Auto-genera recomendaciones cuando cambia el análisis
   */
  React.useEffect(() => {
    if (autoGenerate && analysis && analysis.status === ViabilityStatus.RED) {
      generateRecommendations();
    }
  }, [analysis, autoGenerate, generateRecommendations]);

  /**
   * Selecciona una alternativa específica
   */
  const selectAlternative = useCallback((alternative: AlternativeRecommendation) => {
    setSelectedAlternative(alternative);
  }, []);

  /**
   * Limpia la selección actual
   */
  const clearSelection = useCallback(() => {
    setSelectedAlternative(null);
  }, []);

  /**
   * Obtiene alternativas por categoría
   */
  const getAlternativesByCategory = useCallback((categoryId: string): AlternativeRecommendation[] => {
    const category = state.categories.find(cat => cat.id === categoryId);
    return category?.alternatives || [];
  }, [state.categories]);

  /**
   * Obtiene estadísticas de las alternativas
   */
  const statistics = useMemo(() => {
    const total = state.bestAlternatives.length;
    const green = state.bestAlternatives.filter(alt => alt.viabilityImprovement === ViabilityStatus.GREEN).length;
    const amber = state.bestAlternatives.filter(alt => alt.viabilityImprovement === ViabilityStatus.AMBER).length;
    const red = state.bestAlternatives.filter(alt => alt.viabilityImprovement === ViabilityStatus.RED).length;

    return {
      total,
      green,
      amber,
      red,
      safePercentage: total > 0 ? Math.round(((green + amber) / total) * 100) : 0
    };
  }, [state.bestAlternatives]);

  /**
   * Verifica si hay alternativas disponibles
   */
  const hasAlternatives = state.bestAlternatives.length > 0;

  /**
   * Verifica si hay alternativas seguras
   */
  const hasSafeAlternatives = state.safeAlternatives.length > 0;

  return {
    // Estado
    ...state,
    selectedAlternative,
    
    // Estadísticas
    statistics,
    hasAlternatives,
    hasSafeAlternatives,
    
    // Acciones
    generateRecommendations,
    selectAlternative,
    clearSelection,
    getAlternativesByCategory,
    
    // Utilidades
    context: generateContext()
  };
};

/**
 * Determina la condición del cabello desde el análisis de viabilidad
 */
function determineHairConditionFromAnalysis(
  analysis: SmartViabilityAnalysis
): 'excellent' | 'good' | 'fair' | 'poor' | 'damaged' {
  const criticalRisks = analysis.riskFactors.filter(rf => rf.severity === 'critical').length;
  const highRisks = analysis.riskFactors.filter(rf => rf.severity === 'high').length;
  const totalRisks = analysis.riskFactors.length;

  if (criticalRisks > 0) return 'damaged';
  if (highRisks > 1) return 'poor';
  if (totalRisks > 3) return 'fair';
  if (totalRisks > 0) return 'good';
  return 'excellent';
}

/**
 * Hook simplificado para casos específicos
 */
export const useQuickAlternatives = (analysis?: SmartViabilityAnalysis) => {
  const { bestAlternatives, safeAlternatives, hasAlternatives } = useAlternativeRecommendations({
    analysis,
    autoGenerate: true
  });

  return {
    alternatives: bestAlternatives,
    safeAlternatives,
    hasAlternatives,
    quickSelect: (index: number) => bestAlternatives[index] || null
  };
};

export default useAlternativeRecommendations;
