import { useState, useCallback, useEffect, useRef } from 'react';
import { Animated } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Haptics from 'expo-haptics';
import { VisualFormulationData } from '@/types/visual-formulation';
import { logger } from '@/utils/logger';

// Flow step definition
export interface FlowStep {
  id: string;
  title: string;
  icon: React.ComponentType<{ size: number; color: string }>;
  color: string;
  estimatedDuration?: number; // in minutes
  requirements?: string[];
  isOptional?: boolean;
}

// Flow state interface
interface FlowState {
  currentStep: number;
  completedSteps: Set<number>;
  checkedItems: Set<string>;
  startTime: number | null;
  stepStartTimes: Record<number, number>;
  pausedTime: number;
  lastSavedAt: number;
}

// Hook options
interface UseInstructionFlowOptions {
  steps: FlowStep[];
  formulaData: VisualFormulationData;
  autoSave?: boolean;
  onStepChange?: (step: number) => void;
  onComplete?: () => void;
  persistenceKey?: string;
}

// Return interface
export interface UseInstructionFlowReturn {
  // Current state
  currentStep: number;
  completedSteps: Set<number>;
  checkedItems: Set<string>;
  totalSteps: number;

  // Progress calculations
  progress: number;
  stepProgress: number;
  isComplete: boolean;
  canAdvance: boolean;
  canGoBack: boolean;

  // Navigation actions
  advanceStep: () => Promise<void>;
  goToPreviousStep: () => Promise<void>;
  goToStep: (stepIndex: number) => Promise<void>;
  completeFlow: () => Promise<void>;

  // Step management
  markStepComplete: (stepIndex: number) => void;
  isStepCompleted: (stepIndex: number) => boolean;
  getCurrentStepData: () => FlowStep;

  // Checklist management
  toggleChecklistItem: (itemId: string) => Promise<void>;
  isItemChecked: (itemId: string) => boolean;
  getChecklistProgress: () => number;

  // Time tracking
  getElapsedTime: () => number;
  getStepElapsedTime: () => number;
  getEstimatedTimeRemaining: () => number;

  // Persistence
  saveProgress: () => Promise<void>;
  loadProgress: () => Promise<void>;
  clearProgress: () => Promise<void>;

  // Animation values for UI components
  fadeAnim: Animated.Value;
  slideAnim: Animated.Value;
  progressAnims: Animated.Value[];
}

const STORAGE_PREFIX = 'instruction_flow_';

export function useInstructionFlow({
  steps,
  formulaData,
  autoSave = true,
  onStepChange,
  onComplete,
  persistenceKey = 'default',
}: UseInstructionFlowOptions): UseInstructionFlowReturn {
  // Core state
  const [flowState, setFlowState] = useState<FlowState>({
    currentStep: 0,
    completedSteps: new Set<number>(),
    checkedItems: new Set<string>(),
    startTime: null,
    stepStartTimes: {},
    pausedTime: 0,
    lastSavedAt: Date.now(),
  });

  // Animation refs
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const slideAnim = useRef(new Animated.Value(0)).current;
  const progressAnims = useRef(steps.map(() => new Animated.Value(1))).current;

  // Storage key
  const storageKey = `${STORAGE_PREFIX}${persistenceKey}_${formulaData.id || 'unknown'}`;

  // Initialize flow state
  useEffect(() => {
    if (autoSave) {
      loadProgress();
    }

    // Start timing if not already started
    if (!flowState.startTime) {
      setFlowState(prev => ({
        ...prev,
        startTime: Date.now(),
        stepStartTimes: { ...prev.stepStartTimes, [prev.currentStep]: Date.now() },
      }));
    }
  }, [autoSave, flowState.startTime, loadProgress]);

  // Auto-save on state changes
  useEffect(() => {
    if (autoSave && flowState.startTime) {
      const debounceTimer = setTimeout(() => {
        saveProgress();
      }, 1000);

      return () => clearTimeout(debounceTimer);
    }
  }, [flowState, autoSave, saveProgress]);

  // Trigger step change callback
  useEffect(() => {
    onStepChange?.(flowState.currentStep);
  }, [flowState.currentStep, onStepChange]);

  // Progress calculations
  const progress = steps.length > 0 ? flowState.currentStep / steps.length : 0;
  const stepProgress = flowState.completedSteps ? (flowState.completedSteps.size / steps.length) : 0;
  const isComplete =
    flowState.currentStep >= steps.length - 1 && flowState.completedSteps && flowState.completedSteps.has(steps.length - 1);
  const canAdvance = flowState.currentStep < steps.length - 1;
  const canGoBack = flowState.currentStep > 0;

  // Step validation logic
  const validateStepCompletion = useCallback(
    (stepIndex: number): boolean => {
      const step = steps[stepIndex];
      if (!step) return false;

      // Optional steps can always be completed
      if (step.isOptional) return true;

      // Check if step has requirements
      if (step.requirements && step.requirements.length > 0) {
        return step.requirements.every(requirement => flowState.checkedItems.has(requirement));
      }

      return true;
    },
    [steps, flowState.checkedItems]
  );

  const canAdvanceToNext = useCallback((): boolean => {
    if (!canAdvance) return false;
    return validateStepCompletion(flowState.currentStep);
  }, [canAdvance, validateStepCompletion, flowState.currentStep]);

  // Navigation actions
  const advanceStep = useCallback(async (): Promise<void> => {
    if (!canAdvanceToNext()) return;

    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    if (flowState.currentStep < steps.length - 1) {
      const nextStep = flowState.currentStep + 1;

      setFlowState(prev => ({
        ...prev,
        currentStep: nextStep,
        completedSteps: new Set([...prev.completedSteps, prev.currentStep]),
        stepStartTimes: { ...prev.stepStartTimes, [nextStep]: Date.now() },
      }));
    } else {
      await completeFlow();
    }
  }, [canAdvanceToNext, flowState.currentStep, steps.length, completeFlow]);

  const goToPreviousStep = useCallback(async (): Promise<void> => {
    if (!canGoBack) return;

    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    const prevStep = flowState.currentStep - 1;
    setFlowState(prev => ({
      ...prev,
      currentStep: prevStep,
      stepStartTimes: { ...prev.stepStartTimes, [prevStep]: Date.now() },
    }));
  }, [canGoBack, flowState.currentStep]);

  const goToStep = useCallback(
    async (stepIndex: number): Promise<void> => {
      if (stepIndex < 0 || stepIndex >= steps.length) return;
      if (stepIndex > flowState.currentStep && !flowState.completedSteps.has(stepIndex - 1)) {
        // Can't skip ahead unless previous steps are completed
        return;
      }

      await Haptics.selectionAsync();

      setFlowState(prev => ({
        ...prev,
        currentStep: stepIndex,
        stepStartTimes: { ...prev.stepStartTimes, [stepIndex]: Date.now() },
      }));
    },
    [steps.length, flowState.currentStep, flowState.completedSteps]
  );

  const completeFlow = useCallback(async (): Promise<void> => {
    await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

    setFlowState(prev => ({
      ...prev,
      completedSteps: new Set([...prev.completedSteps, prev.currentStep]),
    }));

    onComplete?.();
  }, [onComplete]);

  // Step management
  const markStepComplete = useCallback(
    (stepIndex: number): void => {
      if (stepIndex < 0 || stepIndex >= steps.length) return;

      setFlowState(prev => ({
        ...prev,
        completedSteps: new Set([...prev.completedSteps, stepIndex]),
      }));
    },
    [steps.length]
  );

  const isStepCompleted = useCallback(
    (stepIndex: number): boolean => {
      return flowState.completedSteps.has(stepIndex);
    },
    [flowState.completedSteps]
  );

  const getCurrentStepData = useCallback((): FlowStep => {
    return steps[flowState.currentStep] || steps[0];
  }, [steps, flowState.currentStep]);

  // Checklist management
  const toggleChecklistItem = useCallback(async (itemId: string): Promise<void> => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    setFlowState(prev => {
      const newCheckedItems = new Set(prev.checkedItems);
      if (newCheckedItems.has(itemId)) {
        newCheckedItems.delete(itemId);
      } else {
        newCheckedItems.add(itemId);
      }

      return {
        ...prev,
        checkedItems: newCheckedItems,
      };
    });
  }, []);

  const isItemChecked = useCallback(
    (itemId: string): boolean => {
      return flowState.checkedItems.has(itemId);
    },
    [flowState.checkedItems]
  );

  const getChecklistProgress = useCallback((): number => {
    const currentStep = getCurrentStepData();
    if (!currentStep.requirements || currentStep.requirements.length === 0) {
      return 100;
    }

    const completedRequirements = currentStep.requirements.filter(req =>
      flowState.checkedItems.has(req)
    ).length;

    return (completedRequirements / currentStep.requirements.length) * 100;
  }, [getCurrentStepData, flowState.checkedItems]);

  // Time tracking
  const getElapsedTime = useCallback((): number => {
    if (!flowState.startTime) return 0;
    return Math.floor((Date.now() - flowState.startTime - flowState.pausedTime) / 1000);
  }, [flowState.startTime, flowState.pausedTime]);

  const getStepElapsedTime = useCallback((): number => {
    const stepStartTime = flowState.stepStartTimes[flowState.currentStep];
    if (!stepStartTime) return 0;
    return Math.floor((Date.now() - stepStartTime) / 1000);
  }, [flowState.stepStartTimes, flowState.currentStep]);

  const getEstimatedTimeRemaining = useCallback((): number => {
    const remainingSteps = steps.slice(flowState.currentStep);
    const estimatedMinutes = remainingSteps.reduce(
      (total, step) => total + (step.estimatedDuration || 5),
      0
    );
    return estimatedMinutes * 60; // Convert to seconds
  }, [steps, flowState.currentStep]);

  // Persistence methods
  const saveProgress = useCallback(async (): Promise<void> => {
    try {
      const dataToSave = {
        ...flowState,
        completedSteps: Array.from(flowState.completedSteps),
        checkedItems: Array.from(flowState.checkedItems),
        lastSavedAt: Date.now(),
      };

      await AsyncStorage.setItem(storageKey, JSON.stringify(dataToSave));
    } catch (error) {
      logger.warn('Failed to save instruction flow progress', 'useInstructionFlow', error);
    }
  }, [flowState, storageKey]);

  const loadProgress = useCallback(async (): Promise<void> => {
    try {
      const savedData = await AsyncStorage.getItem(storageKey);
      if (savedData) {
        const parsedData = JSON.parse(savedData);

        setFlowState({
          ...parsedData,
          completedSteps: new Set(parsedData.completedSteps || []),
          checkedItems: new Set(parsedData.checkedItems || []),
        });
      }
    } catch (error) {
      logger.warn('Failed to load instruction flow progress', 'useInstructionFlow', error);
    }
  }, [storageKey]);

  const clearProgress = useCallback(async (): Promise<void> => {
    try {
      await AsyncStorage.removeItem(storageKey);
      setFlowState({
        currentStep: 0,
        completedSteps: new Set<number>(),
        checkedItems: new Set<string>(),
        startTime: Date.now(),
        stepStartTimes: { 0: Date.now() },
        pausedTime: 0,
        lastSavedAt: Date.now(),
      });
    } catch (error) {
      logger.warn('Failed to clear instruction flow progress', 'useInstructionFlow', error);
    }
  }, [storageKey]);

  return {
    // Current state
    currentStep: flowState.currentStep,
    completedSteps: flowState.completedSteps,
    checkedItems: flowState.checkedItems,
    totalSteps: steps.length,

    // Progress calculations
    progress,
    stepProgress,
    isComplete,
    canAdvance: canAdvanceToNext(),
    canGoBack,

    // Navigation actions
    advanceStep,
    goToPreviousStep,
    goToStep,
    completeFlow,

    // Step management
    markStepComplete,
    isStepCompleted,
    getCurrentStepData,

    // Checklist management
    toggleChecklistItem,
    isItemChecked,
    getChecklistProgress,

    // Time tracking
    getElapsedTime,
    getStepElapsedTime,
    getEstimatedTimeRemaining,

    // Persistence
    saveProgress,
    loadProgress,
    clearProgress,

    // Animation values for UI components
    fadeAnim,
    slideAnim,
    progressAnims,
  };
}
