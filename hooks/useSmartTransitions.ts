import { useState, useCallback, useEffect } from 'react';
import { 
  validateStepTransition, 
  getTransitionType,
  TransitionValidation,
  TransitionStatus,
  TransitionType,
  TransitionContext,
  TransitionRecommendation
} from '@/utils/smart-transitions';
import { ServiceData } from '@/src/service/hooks/useServiceFlow';
import { SmartViabilityAnalysis } from '@/utils/viability-analyzer';

interface UseSmartTransitionsProps {
  serviceData: ServiceData;
  currentStep: number;
  viabilityAnalysis?: SmartViabilityAnalysis;
  onTransitionBlocked?: (validation: TransitionValidation) => void;
  onTransitionWarning?: (validation: TransitionValidation) => void;
}

interface UseSmartTransitionsReturn {
  // State
  currentValidation: TransitionValidation | null;
  isDialogVisible: boolean;
  pendingTransition: { targetStep: number } | null;
  
  // Actions
  validateTransition: (targetStep: number) => Promise<boolean>;
  showTransitionDialog: (validation: TransitionValidation, targetStep: number) => void;
  hideTransitionDialog: () => void;
  proceedWithTransition: () => void;
  cancelTransition: () => void;
  handleRecommendation: (recommendation: TransitionRecommendation) => void;
  
  // Helpers
  canProceedToStep: (targetStep: number) => boolean;
  getValidationForStep: (targetStep: number) => TransitionValidation | null;
  hasWarningsForStep: (targetStep: number) => boolean;
  isStepBlocked: (targetStep: number) => boolean;
}

export const useSmartTransitions = ({
  serviceData,
  currentStep,
  viabilityAnalysis,
  onTransitionBlocked,
  onTransitionWarning,
}: UseSmartTransitionsProps): UseSmartTransitionsReturn => {
  const [currentValidation, setCurrentValidation] = useState<TransitionValidation | null>(null);
  const [isDialogVisible, setIsDialogVisible] = useState(false);
  const [pendingTransition, setPendingTransition] = useState<{ targetStep: number } | null>(null);
  const [validationCache, setValidationCache] = useState<Map<string, TransitionValidation>>(new Map());

  /**
   * Valida una transición específica
   */
  const validateTransition = useCallback(async (targetStep: number): Promise<boolean> => {
    // No validar si vamos hacia atrás
    if (targetStep <= currentStep) {
      return true;
    }

    const transitionType = getTransitionType(currentStep, targetStep);
    if (!transitionType) {
      return true; // Permitir transiciones no definidas
    }

    // Verificar cache
    const cacheKey = `${currentStep}-${targetStep}`;
    let validation = validationCache.get(cacheKey);

    if (!validation) {
      // Crear contexto de transición
      const context: TransitionContext = {
        serviceData,
        currentStep,
        targetStep,
        viabilityAnalysis,
      };

      // Validar transición
      validation = validateStepTransition(transitionType, context);
      
      // Cachear resultado
      setValidationCache(prev => new Map(prev).set(cacheKey, validation!));
    }

    setCurrentValidation(validation);

    // Manejar según el estado de la validación
    switch (validation.status) {
      case TransitionStatus.PROCEED:
        return true;

      case TransitionStatus.WARNING:
        onTransitionWarning?.(validation);
        showTransitionDialog(validation, targetStep);
        return false; // Mostrar diálogo primero

      case TransitionStatus.CONFIRMATION:
        showTransitionDialog(validation, targetStep);
        return false; // Requiere confirmación

      case TransitionStatus.BLOCKED:
        onTransitionBlocked?.(validation);
        showTransitionDialog(validation, targetStep);
        return false; // Bloqueado

      default:
        return true;
    }
  }, [currentStep, serviceData, viabilityAnalysis, onTransitionBlocked, onTransitionWarning, validationCache]);

  /**
   * Muestra el diálogo de transición
   */
  const showTransitionDialog = useCallback((validation: TransitionValidation, targetStep: number) => {
    setCurrentValidation(validation);
    setPendingTransition({ targetStep });
    setIsDialogVisible(true);
  }, []);

  /**
   * Oculta el diálogo de transición
   */
  const hideTransitionDialog = useCallback(() => {
    setIsDialogVisible(false);
    setPendingTransition(null);
    setCurrentValidation(null);
  }, []);

  /**
   * Procede con la transición pendiente
   */
  const proceedWithTransition = useCallback(() => {
    if (pendingTransition && currentValidation?.canProceed) {
      hideTransitionDialog();
      // La navegación real debe ser manejada por el componente padre
      return pendingTransition.targetStep;
    }
    return null;
  }, [pendingTransition, currentValidation, hideTransitionDialog]);

  /**
   * Cancela la transición pendiente
   */
  const cancelTransition = useCallback(() => {
    hideTransitionDialog();
  }, [hideTransitionDialog]);

  /**
   * Maneja la selección de una recomendación
   */
  const handleRecommendation = useCallback((recommendation: TransitionRecommendation) => {
    // Ejecutar acción de la recomendación si existe
    if (recommendation.action) {
      recommendation.action();
    }

    // Cerrar diálogo para recomendaciones de tipo acción
    if (recommendation.type === 'action') {
      hideTransitionDialog();
    }
  }, [hideTransitionDialog]);

  /**
   * Verifica si se puede proceder a un paso específico
   */
  const canProceedToStep = useCallback((targetStep: number): boolean => {
    if (targetStep <= currentStep) {
      return true; // Siempre se puede ir hacia atrás
    }

    const transitionType = getTransitionType(currentStep, targetStep);
    if (!transitionType) {
      return true; // Permitir transiciones no definidas
    }

    const context: TransitionContext = {
      serviceData,
      currentStep,
      targetStep,
      viabilityAnalysis,
    };

    const validation = validateStepTransition(transitionType, context);
    return validation.canProceed;
  }, [currentStep, serviceData, viabilityAnalysis]);

  /**
   * Obtiene la validación para un paso específico
   */
  const getValidationForStep = useCallback((targetStep: number): TransitionValidation | null => {
    const transitionType = getTransitionType(currentStep, targetStep);
    if (!transitionType) {
      return null;
    }

    const context: TransitionContext = {
      serviceData,
      currentStep,
      targetStep,
      viabilityAnalysis,
    };

    return validateStepTransition(transitionType, context);
  }, [currentStep, serviceData, viabilityAnalysis]);

  /**
   * Verifica si hay advertencias para un paso específico
   */
  const hasWarningsForStep = useCallback((targetStep: number): boolean => {
    const validation = getValidationForStep(targetStep);
    return validation?.status === TransitionStatus.WARNING || 
           validation?.status === TransitionStatus.CONFIRMATION;
  }, [getValidationForStep]);

  /**
   * Verifica si un paso está bloqueado
   */
  const isStepBlocked = useCallback((targetStep: number): boolean => {
    const validation = getValidationForStep(targetStep);
    return validation?.status === TransitionStatus.BLOCKED;
  }, [getValidationForStep]);

  // Limpiar cache cuando cambian los datos del servicio
  useEffect(() => {
    setValidationCache(new Map());
  }, [serviceData.desiredAnalysisResult, serviceData.formula, serviceData.viabilityAnalysis]);

  return {
    // State
    currentValidation,
    isDialogVisible,
    pendingTransition,
    
    // Actions
    validateTransition,
    showTransitionDialog,
    hideTransitionDialog,
    proceedWithTransition,
    cancelTransition,
    handleRecommendation,
    
    // Helpers
    canProceedToStep,
    getValidationForStep,
    hasWarningsForStep,
    isStepBlocked,
  };
};

/**
 * Hook simplificado para validación básica de transiciones
 */
export const useTransitionValidation = (
  serviceData: ServiceData,
  currentStep: number
) => {
  const canGoToStep = useCallback((targetStep: number): boolean => {
    // Lógica básica de validación
    if (targetStep <= currentStep) return true;
    
    // Validaciones específicas por paso
    switch (targetStep) {
      case 1: // Desired Color Step
        return !!(serviceData.overallTone && serviceData.zoneColorAnalysis);
      case 2: // Formulation Step
        return !!(serviceData.desiredAnalysisResult);
      case 3: // Completion Step
        return !!(serviceData.formula && serviceData.selectedBrand);
      default:
        return true;
    }
  }, [serviceData, currentStep]);

  const getStepStatus = useCallback((stepIndex: number): 'available' | 'warning' | 'blocked' => {
    if (stepIndex <= currentStep) return 'available';
    if (canGoToStep(stepIndex)) return 'available';
    
    // Determinar si es advertencia o bloqueo
    const hasBasicData = stepIndex === 1 ? 
      !!serviceData.overallTone : 
      stepIndex === 2 ? 
        !!serviceData.desiredAnalysisResult : 
        !!serviceData.formula;
    
    return hasBasicData ? 'warning' : 'blocked';
  }, [currentStep, canGoToStep, serviceData]);

  return {
    canGoToStep,
    getStepStatus,
  };
};

export default useSmartTransitions;
