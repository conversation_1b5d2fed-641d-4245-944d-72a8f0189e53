import { useState, useEffect, useCallback } from 'react';
import { analyzeSmartViability, SmartViabilityAnalysis, ViabilityStatus } from '@/utils/viability-analyzer';
import { HairZone, ZoneColorAnalysis } from '@/types/hair-diagnosis';
import { DesiredColorAnalysisResult } from '@/types/desired-analysis';

interface UseSmartViabilityProps {
  analysisResult?: {
    level?: number;
    averageLevel?: number;
    averageDepthLevel?: number;
    [key: string]: unknown;
  };
  desiredAnalysisResult?: DesiredColorAnalysisResult;
  zoneColorAnalysis?: Record<HairZone, Partial<ZoneColorAnalysis>>;
  autoAnalyze?: boolean;
}

interface UseSmartViabilityReturn {
  analysis: SmartViabilityAnalysis | null;
  isAnalyzing: boolean;
  error: string | null;
  
  // Actions
  analyzeViability: () => void;
  clearAnalysis: () => void;
  
  // Status helpers
  isGreen: boolean;
  isAmber: boolean;
  isRed: boolean;
  canProceedDirectly: boolean;
  requiresMultipleSessions: boolean;
  isNotRecommended: boolean;
  
  // Session plan helpers
  totalSessions: number;
  estimatedTimeframe: string;
  hasRisks: boolean;
  criticalRisks: number;
  hasAlternatives: boolean;
}

export const useSmartViability = ({
  analysisResult,
  desiredAnalysisResult,
  zoneColorAnalysis,
  autoAnalyze = true,
}: UseSmartViabilityProps): UseSmartViabilityReturn => {
  const [analysis, setAnalysis] = useState<SmartViabilityAnalysis | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const analyzeViability = useCallback(() => {
    if (!analysisResult || !desiredAnalysisResult || !zoneColorAnalysis) {
      setError('Datos insuficientes para análisis de viabilidad');
      return;
    }

    setIsAnalyzing(true);
    setError(null);

    try {
      const result = analyzeSmartViability(
        analysisResult,
        desiredAnalysisResult,
        zoneColorAnalysis
      );
      
      setAnalysis(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error en análisis de viabilidad');
      setAnalysis(null);
    } finally {
      setIsAnalyzing(false);
    }
  }, [analysisResult, desiredAnalysisResult, zoneColorAnalysis]);

  const clearAnalysis = useCallback(() => {
    setAnalysis(null);
    setError(null);
  }, []);

  // Auto-analyze when dependencies change
  useEffect(() => {
    if (autoAnalyze && analysisResult && desiredAnalysisResult && zoneColorAnalysis) {
      analyzeViability();
    }
  }, [autoAnalyze, analyzeViability]);

  // Status helpers
  const isGreen = analysis?.status === ViabilityStatus.GREEN;
  const isAmber = analysis?.status === ViabilityStatus.AMBER;
  const isRed = analysis?.status === ViabilityStatus.RED;
  
  const canProceedDirectly = isGreen;
  const requiresMultipleSessions = isAmber;
  const isNotRecommended = isRed;

  // Session plan helpers
  const totalSessions = analysis?.sessionPlan.totalSessions || 0;
  const estimatedTimeframe = analysis?.sessionPlan.estimatedTimeframe || '';
  
  // Risk helpers
  const hasRisks = (analysis?.riskFactors.length || 0) > 0;
  const criticalRisks = analysis?.riskFactors.filter(r => r.severity === 'critical').length || 0;
  const hasAlternatives = (analysis?.alternatives?.length || 0) > 0;

  return {
    analysis,
    isAnalyzing,
    error,
    
    // Actions
    analyzeViability,
    clearAnalysis,
    
    // Status helpers
    isGreen,
    isAmber,
    isRed,
    canProceedDirectly,
    requiresMultipleSessions,
    isNotRecommended,
    
    // Session plan helpers
    totalSessions,
    estimatedTimeframe,
    hasRisks,
    criticalRisks,
    hasAlternatives,
  };
};

/**
 * Hook simplificado para casos donde solo necesitas el estado del semáforo
 */
export const useViabilityStatus = (
  analysisResult?: any,
  desiredAnalysisResult?: DesiredColorAnalysisResult,
  zoneColorAnalysis?: Record<HairZone, Partial<ZoneColorAnalysis>>
): {
  status: ViabilityStatus | null;
  isGreen: boolean;
  isAmber: boolean;
  isRed: boolean;
  canProceed: boolean;
} => {
  const { analysis, isGreen, isAmber, isRed, canProceedDirectly } = useSmartViability({
    analysisResult,
    desiredAnalysisResult,
    zoneColorAnalysis,
    autoAnalyze: true,
  });

  return {
    status: analysis?.status || null,
    isGreen,
    isAmber,
    isRed,
    canProceed: canProceedDirectly || isAmber, // Amber también puede proceder con plan
  };
};

/**
 * Hook para obtener recomendaciones de acción basadas en el estado
 */
export const useViabilityActions = (analysis: SmartViabilityAnalysis | null) => {
  const getRecommendedActions = useCallback(() => {
    if (!analysis) return [];

    const actions: Array<{
      id: string;
      label: string;
      type: 'primary' | 'secondary' | 'warning' | 'danger';
      description: string;
    }> = [];

    switch (analysis.status) {
      case ViabilityStatus.GREEN:
        actions.push({
          id: 'proceed',
          label: 'Proceder con Formulación',
          type: 'primary',
          description: 'El cabello está en condiciones óptimas para el proceso'
        });
        break;

      case ViabilityStatus.AMBER:
        actions.push({
          id: 'accept_plan',
          label: 'Aceptar Plan Gradual',
          type: 'primary',
          description: `Proceso en ${analysis.sessionPlan.totalSessions} sesiones durante ${analysis.sessionPlan.estimatedTimeframe}`
        });
        actions.push({
          id: 'view_alternatives',
          label: 'Ver Alternativas',
          type: 'secondary',
          description: 'Explorar opciones más directas'
        });
        break;

      case ViabilityStatus.RED:
        actions.push({
          id: 'view_alternatives',
          label: 'Ver Alternativas Seguras',
          type: 'primary',
          description: 'Opciones que no comprometen la salud capilar'
        });
        actions.push({
          id: 'request_treatment',
          label: 'Solicitar Tratamiento',
          type: 'secondary',
          description: 'Preparar el cabello antes del cambio de color'
        });
        if (analysis.riskFactors.some(r => r.severity !== 'critical')) {
          actions.push({
            id: 'proceed_with_risks',
            label: 'Proceder Bajo Riesgo',
            type: 'danger',
            description: 'Solo con consentimiento informado del cliente'
          });
        }
        break;
    }

    return actions;
  }, [analysis]);

  const getPrimaryAction = useCallback(() => {
    const actions = getRecommendedActions();
    return actions.find(a => a.type === 'primary') || null;
  }, [getRecommendedActions]);

  const getSecondaryActions = useCallback(() => {
    const actions = getRecommendedActions();
    return actions.filter(a => a.type !== 'primary');
  }, [getRecommendedActions]);

  return {
    getRecommendedActions,
    getPrimaryAction,
    getSecondaryActions,
  };
};

export default useSmartViability;
