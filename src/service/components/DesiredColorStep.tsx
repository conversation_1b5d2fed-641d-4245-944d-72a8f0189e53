import React, { useState, useRef } from 'react';
import { logger } from '@/utils/logger';
import { View, Text, TouchableOpacity, ScrollView, StyleSheet, Alert } from 'react-native';
import { Zap, Eye, AlertTriangle, Sparkles } from 'lucide-react-native';
import * as ImagePicker from 'expo-image-picker';
import * as Haptics from 'expo-haptics';
import { BeautyCard, BeautyButton } from '@/components/beauty';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { HairZone } from '@/types/hair-diagnosis';
import { DesiredPhoto, DesiredPhotoType } from '@/types/desired-photo';
import { DesiredColorAnalysisResult } from '@/types/desired-analysis';
import { MaintenanceLevel, BudgetLevel } from '@/types/lifestyle-preferences';
import { ServiceData } from '@/src/service/hooks/useServiceFlow';
import { usePhotoAnalysis } from '@/src/service/hooks/usePhotoAnalysis';
import { useAIAnalysisStore } from '@/stores/ai-analysis-store';
import { aggregateDesiredPhotoAnalyses } from '@/src/service/utils/serviceHelpers';
import { analyzeServiceViability } from '@/utils/viability-analyzer';

// NEW: Import Colorimetry Guardian
import {
  ColorimetryGuardian,
  type PreValidationRequest,
  type PreValidationResult
} from '@/supabase/functions/salonier-assistant/utils/formula-validator';
import { ColorimetryGuardianDialog } from '@/components/colorimetry/ColorimetryGuardianDialog';

// Import existing components
import DesiredPhotoGallery from '@/components/DesiredPhotoGallery';
import DesiredColorAnalysisForm from '@/components/DesiredColorAnalysisForm';
import ViabilityIndicator from '@/components/ViabilityIndicator';
import { DiagnosisSummary } from '@/components/ui/DiagnosisSummary';
import AIResultNotification from '@/components/AIResultNotification';
import { PhotoAnalysisLoading } from '@/components/animation/PhotoAnalysisLoading';
import { ConfidenceIndicator } from '@/components/ai/ConfidenceIndicator';
import Colors from '@/constants/colors';

interface DesiredColorStepProps {
  data: ServiceData;
  onUpdate: (updates: Partial<ServiceData>) => void;
  onNext: () => void;
  onBack?: () => void;
  onSave?: () => void;
  onSaveSilent?: () => void;
}

interface AIAnalysisExtended {
  detectedLevel?: number;
  detectedTone?: string;
  detectedTechnique?: string;
  customTechnique?: string;
  contrast?: 'subtle' | 'medium' | 'high';
  direction?: 'warmer' | 'cooler' | 'neutral';
  graysCoverage?: number;
  finalTexture?: 'matte' | 'natural' | 'glossy';
  specialNotes?: string;
  zoneAnalysis?: unknown;
}

// analyzeServiceViability is now imported from utils/viability-analyzer.ts

export const DesiredColorStep: React.FC<DesiredColorStepProps> = ({
  data,
  onUpdate,
  onNext,
  onBack: _onBack,
  onSave: _onSave,
  onSaveSilent,
}) => {
  const [isAnalyzingDesired, setIsAnalyzingDesired] = useState(false);
  const [isRecalculatingViability, setIsRecalculatingViability] = useState(false);
  const [showAINotification, setShowAINotification] = useState(false);
  const [aiFieldsCount, setAIFieldsCount] = useState(0);

  // NEW: Colorimetry Guardian state
  const [showGuardianDialog, setShowGuardianDialog] = useState(false);
  const [guardianValidation, setGuardianValidation] = useState<PreValidationResult | null>(null);

  // ScrollView ref for auto-scroll
  const scrollRef = useRef<ScrollView>(null);

  // Track desired capture step without causing re-renders
  const currentDesiredPhotoTypeRef = useRef<DesiredPhotoType>(DesiredPhotoType.MAIN);

  // Debounce timer para optimizar performance
  const viabilityRecalcTimeout = useRef<ReturnType<typeof setTimeout> | null>(null);

  // Extract complex expressions for static checking
  const overallLevel = data.desiredAnalysisResult?.general?.overallLevel;
  const overallTone = data.desiredAnalysisResult?.general?.overallTone;
  const rootsDesiredLevel = data.desiredAnalysisResult?.zones?.[HairZone.ROOTS]?.desiredLevel;
  const midsDesiredLevel = data.desiredAnalysisResult?.zones?.[HairZone.MIDS]?.desiredLevel;
  const endsDesiredLevel = data.desiredAnalysisResult?.zones?.[HairZone.ENDS]?.desiredLevel;

  // Recalcular viabilidad automáticamente cuando el usuario edite cualquier valor relevante
  React.useEffect(() => {
    // Limpiar timeout anterior si existe
    if (viabilityRecalcTimeout.current) {
      clearTimeout(viabilityRecalcTimeout.current);
    }

    // Solo recalcular si tenemos los datos mínimos necesarios
    if (!data.desiredAnalysisResult || !data.hairThickness || !data.zoneColorAnalysis) {
      return;
    }

    // Debounce el cálculo para evitar cálculos excesivos mientras el usuario edita
    viabilityRecalcTimeout.current = setTimeout(() => {
      // Extraer valores clave para comparación
      const currentLevel = data.zoneColorAnalysis?.[HairZone.ROOTS]?.level || 6;
      const _desiredLevel =
        parseInt(data.desiredAnalysisResult?.general?.overallLevel?.split('/')[0] || '7') || 7;
      const _desiredTone = data.desiredAnalysisResult?.general?.overallTone;

      // Crear el diagnosis object
      const diagnosis = {
        hairThickness: data.hairThickness,
        hairDensity: data.hairDensity,
        overallTone: data.overallTone,
        overallReflect: data.overallReflect || data.overallUndertone,
        averageLevel: currentLevel,
        zoneAnalysis: data.zoneColorAnalysis,
        zonePhysicalAnalysis: data.zonePhysicalAnalysis,
      };

      // Calcular nueva viabilidad
      const newViabilityAnalysis = data.desiredAnalysisResult
        ? analyzeServiceViability(diagnosis, data.desiredAnalysisResult, data.zoneColorAnalysis)
        : null;

      // Solo actualizar si hay cambios significativos en la viabilidad
      const hasSignificantChange =
        newViabilityAnalysis &&
        (!data.viabilityAnalysis ||
          data.viabilityAnalysis.score !== newViabilityAnalysis.score ||
          data.viabilityAnalysis.factors.levelDifference !==
            newViabilityAnalysis.factors.levelDifference ||
          data.viabilityAnalysis.factors.hairHealth !== newViabilityAnalysis.factors.hairHealth);

      if (hasSignificantChange) {
        // Debug logging removed for production

        // Mostrar brevemente que se está recalculando
        setIsRecalculatingViability(true);

        onUpdate({ viabilityAnalysis: newViabilityAnalysis });

        // Ocultar indicador después de un breve delay
        setTimeout(() => setIsRecalculatingViability(false), 500);
      }
    }, 300); // Debounce de 300ms
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    // Dependencies específicas que disparan el recálculo
    overallLevel,
    overallTone,
    rootsDesiredLevel,
    midsDesiredLevel,
    endsDesiredLevel,
    data.zoneColorAnalysis,
    data.zonePhysicalAnalysis,
    data.hairThickness,
    data.hairDensity,
    data.overallReflect,
    data.overallUndertone,
    data.desiredAnalysisResult,
    // Note: onUpdate removed to prevent infinite re-renders
    // onUpdate callback is stable within service context
  ]);

  // Cleanup timeout on unmount
  React.useEffect(() => {
    return () => {
      if (viabilityRecalcTimeout.current) {
        clearTimeout(viabilityRecalcTimeout.current);
      }
    };
  }, []);

  // Track if we've shown the notification for current analysis
  const hasShownNotificationRef = useRef(false);

  // Show notification and scroll when AI analysis completes
  React.useEffect(() => {
    if (
      data.desiredAnalysisResult &&
      data.desiredAnalysisResult.isFromAI &&
      !hasShownNotificationRef.current &&
      scrollRef.current
    ) {
      // Count fields that were filled
      let fieldsCount = 0;
      if (data.desiredAnalysisResult.general?.overallTone) fieldsCount++;
      if (data.desiredAnalysisResult.general?.overallLevel) fieldsCount++;
      if (data.desiredAnalysisResult.general?.overallReflect) fieldsCount++;
      if (data.desiredAnalysisResult.zones) {
        fieldsCount += Object.keys(data.desiredAnalysisResult.zones).length * 2;
      }
      if (data.desiredAnalysisResult.compatibility) fieldsCount++;

      setAIFieldsCount(fieldsCount);
      setShowAINotification(true);
      hasShownNotificationRef.current = true;

      // Haptic feedback
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

      // Smooth scroll to results section
      setTimeout(() => {
        scrollRef.current?.scrollTo({ y: 300, animated: true });
      }, 300);
    }

    // Reset the flag when there's no analysis result
    if (!data.desiredAnalysisResult) {
      hasShownNotificationRef.current = false;
    }
  }, [data.desiredAnalysisResult, scrollRef]);

  // AI Analysis Store
  const { analyzeDesiredPhoto } = useAIAnalysisStore();

  const {
    currentPhotoAngle: _currentPhotoAngle,
    setCurrentPhotoAngle: _setCurrentPhotoAngle,
    handleDesiredCameraCapture,
    takePhoto,
  } = usePhotoAnalysis();

  const handleDesiredPhotoAdd = (
    isCamera: boolean,
    useGuidedCapture?: boolean,
    photoType?: DesiredPhotoType
  ) => {
    if (isCamera) {
      // Usar cámara normal
      currentDesiredPhotoTypeRef.current = photoType || DesiredPhotoType.MAIN;
      handleDesiredCameraOpen();
    } else {
      // Usar galería de imágenes
      handleDesiredImagePick();
    }
  };

  const handleDesiredCameraNormal = () => {
    // Usar cámara normal (como en diagnóstico)
    currentDesiredPhotoTypeRef.current = DesiredPhotoType.MAIN;
    handleDesiredCameraOpen();
  };

  const handleDesiredImagePick = async () => {
    try {
      // Usar la función de selección múltiple adaptada para fotos deseadas
      await pickDesiredImages();
    } catch (error) {
      logger.error('Error picking desired images:', error);
    }
  };

  const handleDesiredCameraOpen = async () => {
    try {
      await takePhoto(async uri => {
        // Determinar el tipo de foto basado en las fotos existentes
        let photoType = DesiredPhotoType.MAIN;
        if (data.desiredPhotos.length === 1) {
          photoType = DesiredPhotoType.DETAILS;
        } else if (data.desiredPhotos.length >= 2) {
          photoType = DesiredPhotoType.ALTERNATIVE;
        }

        // Crear nueva foto deseada
        const newPhoto: DesiredPhoto = {
          id: Date.now().toString(),
          uri,
          type: photoType,
          timestamp: new Date(),
        };

        // Actualizar fotos
        const updatedPhotos = [...data.desiredPhotos, newPhoto];
        onUpdate({ desiredPhotos: updatedPhotos });
        // DISABLED: onSave to prevent excessive auto-saving during image processing
        // The interval-based auto-save will handle saving automatically
      });
    } catch (error) {
      logger.error('Error taking desired photo:', error);
    }
  };

  const pickDesiredImages = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsMultipleSelection: true,
        quality: 0.5,
        selectionLimit: 5 - data.desiredPhotos.length,
        base64: false,
      });

      if (!result.canceled && result.assets) {
        const newPhotos: DesiredPhoto[] = result.assets.map((asset, index) => {
          // Determinar el tipo de foto basado en el índice y fotos existentes
          let photoType = DesiredPhotoType.MAIN;
          const totalIndex = data.desiredPhotos.length + index;

          if (totalIndex === 0) {
            photoType = DesiredPhotoType.MAIN;
          } else if (totalIndex === 1) {
            photoType = DesiredPhotoType.DETAILS;
          } else {
            photoType = DesiredPhotoType.ALTERNATIVE;
          }

          return {
            id: Date.now().toString() + index,
            uri: asset.uri,
            type: photoType,
            timestamp: new Date(),
          };
        });

        // Actualizar fotos
        const updatedPhotos = [...data.desiredPhotos, ...newPhotos];
        onUpdate({ desiredPhotos: updatedPhotos });
        // DISABLED: onSave to prevent excessive auto-saving during image processing
        // The interval-based auto-save will handle saving automatically
      }
    } catch (error) {
      logger.error('Error al seleccionar imágenes:', error);
      Alert.alert('Error', 'No se pudo acceder a la galería');
    }
  };

  const handleManualModeSelect = () => {
    onUpdate({ desiredMethod: 'manual' });

    // Inicializar analysisResult si no existe
    if (!data.desiredAnalysisResult) {
      const initialResult: DesiredColorAnalysisResult = {
        general: {
          overallLevel: '',
          overallTone: '',
          technique: '',
          customTechnique: '',
        },
        zones: {
          [HairZone.ROOTS]: {
            zone: HairZone.ROOTS,
            desiredLevel: 0,
            desiredTone: '',
            desiredReflect: '',
            coverage: 100,
          },
          [HairZone.MIDS]: {
            zone: HairZone.MIDS,
            desiredLevel: 0,
            desiredTone: '',
            desiredReflect: '',
            coverage: 100,
          },
          [HairZone.ENDS]: {
            zone: HairZone.ENDS,
            desiredLevel: 0,
            desiredTone: '',
            desiredReflect: '',
            coverage: 100,
          },
        },
        advanced: {
          contrast: 'medium',
          direction: 'neutral',
          graysCoverage: 100,
          finalTexture: 'natural',
          specialNotes: '',
        },
        lifestyle: {
          maintenanceLevel: MaintenanceLevel.MEDIUM,
          avoidTones: [],
          budgetLevel: BudgetLevel.STANDARD,
        },
        confidence: 0,
        isFromAI: false,
      };

      onUpdate({ desiredAnalysisResult: initialResult });
    }
  };

  const analyzeDesiredPhotos = async () => {
    // Set loading state immediately for instant feedback
    setIsAnalyzingDesired(true);

    // Immediate haptic feedback for premium feel
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    } catch {
      // Silently fail if haptics not available
    }

    if (data.desiredPhotos.length === 0) {
      setIsAnalyzingDesired(false);
      Alert.alert('Error', 'Por favor agrega al menos una foto de referencia');
      return;
    }

    if (!data.clientId) {
      setIsAnalyzingDesired(false);
      Alert.alert('Error', 'No se encontró información del cliente. Por favor intenta de nuevo.');
      return;
    }

    try {
      // Construir objeto diagnosis completo desde los datos del servicio
      const diagnosis = {
        hairThickness: data.hairThickness,
        hairDensity: data.hairDensity,
        overallTone: data.overallTone,
        overallReflect: data.overallReflect || data.overallUndertone,
        averageLevel: data.zoneColorAnalysis?.[HairZone.ROOTS]?.level || 6,
        zoneAnalysis: data.zoneColorAnalysis,
        zonePhysicalAnalysis: data.zonePhysicalAnalysis,
        detectedRisks: {
          metallic: false,
          henna: false,
          damaged:
            data.zonePhysicalAnalysis?.[HairZone.ROOTS]?.damage === 'Alto' ||
            data.zonePhysicalAnalysis?.[HairZone.MIDS]?.damage === 'Alto' ||
            data.zonePhysicalAnalysis?.[HairZone.ENDS]?.damage === 'Alto' ||
            false,
          overProcessed: false,
          incompatibleProducts: false,
        },
      };

      // Analizar cada foto con IA real en paralelo para mejor rendimiento
      const photoAnalyses = [];
      const analysisPromises = data.desiredPhotos.map(async photo => {
        try {
          // Debug logging removed for production
          const analysis = await analyzeDesiredPhoto(photo.id, photo.uri, diagnosis, data.clientId);

          if (analysis) {
            // Convertir el resultado de la IA al formato esperado por aggregateDesiredPhotoAnalyses
            const extendedAnalysis = analysis as AIAnalysisExtended;
            return {
              general: {
                overallLevel: extendedAnalysis.detectedLevel?.toString() || '7',
                overallTone: extendedAnalysis.detectedTone || 'Rubio medio',
                technique: extendedAnalysis.detectedTechnique || 'highlights',
                customTechnique: extendedAnalysis.customTechnique || '',
              },
              advanced: {
                contrast: extendedAnalysis.contrast || 'medium',
                direction: extendedAnalysis.direction || 'neutral',
                graysCoverage: extendedAnalysis.graysCoverage || 100,
                finalTexture: extendedAnalysis.finalTexture || 'natural',
                specialNotes: extendedAnalysis.specialNotes || '',
              },
              // Incluir datos de zonas si están disponibles
              zoneAnalysis: extendedAnalysis.zoneAnalysis || null,
            };
          }
          return null;
        } catch (photoError) {
          logger.error(`[analyzeDesiredPhotos] Error analyzing photo ${photo.id}:`, photoError);
          return null;
        }
      });

      // Procesar análisis en paralelo
      const results = await Promise.allSettled(analysisPromises);

      // Filtrar resultados exitosos
      results.forEach(result => {
        if (result.status === 'fulfilled' && result.value) {
          photoAnalyses.push(result.value);
        }
      });

      if (photoAnalyses.length === 0) {
        throw new Error('No se pudo analizar ninguna foto');
      }

      // Agregar los resultados de análisis individuales
      const aggregatedResult = aggregateDesiredPhotoAnalyses(photoAnalyses, data.desiredPhotos);

      // Marcar como resultado de IA (la confianza ya es calculada correctamente en aggregateDesiredPhotoAnalyses)
      aggregatedResult.isFromAI = true;

      // Calcular análisis de viabilidad inmediatamente
      const viabilityAnalysis = analyzeServiceViability(
        diagnosis,
        aggregatedResult,
        data.zoneColorAnalysis
      );

      // Debug logging removed for production

      onUpdate({
        desiredAnalysisResult: aggregatedResult,
        viabilityAnalysis: viabilityAnalysis,
      });
      onSaveSilent?.(); // Use silent save for AI analysis

      // NEW: Run Colorimetry Guardian after AI analysis
      runColorimetryGuardian(aggregatedResult);

      return '✅ Referencias analizadas con IA. Revisa y ajusta según tu criterio';
    } catch (error: unknown) {
      logger.error('[analyzeDesiredPhotos] Error:', error);

      if (error.message === 'TIMEOUT_ERROR') {
        Alert.alert(
          'Análisis tardando más de lo normal',
          'El análisis de las fotos de referencia está tardando más de 30 segundos. ¿Qué deseas hacer?',
          [
            {
              text: 'Continuar esperando',
              onPress: () => analyzeDesiredPhotos(),
            },
            {
              text: 'Análisis manual',
              onPress: () => {
                onUpdate({ desiredMethod: 'manual' });
              },
              style: 'cancel',
            },
          ]
        );
        return;
      }

      // Fallback to mock data if AI fails
      Alert.alert(
        'Error de Conexión con IA',
        'No se pudo analizar las fotos con inteligencia artificial. ¿Deseas usar datos de ejemplo o intentar de nuevo?',
        [
          {
            text: 'Reintentar',
            onPress: () => analyzeDesiredPhotos(),
          },
          {
            text: 'Usar Ejemplo',
            onPress: () => {
              const mockResult: DesiredColorAnalysisResult = {
                general: {
                  overallLevel: '8/9',
                  overallTone: 'Rubio ceniza',
                  technique: 'balayage',
                  customTechnique: '',
                },
                zones: {
                  [HairZone.ROOTS]: {
                    zone: HairZone.ROOTS,
                    desiredLevel: 7,
                    desiredTone: 'Rubio oscuro',
                    desiredReflect: 'Ceniza',
                    coverage: 100,
                  },
                  [HairZone.MIDS]: {
                    zone: HairZone.MIDS,
                    desiredLevel: 8,
                    desiredTone: 'Rubio medio',
                    desiredReflect: 'Beige',
                    coverage: 80,
                  },
                  [HairZone.ENDS]: {
                    zone: HairZone.ENDS,
                    desiredLevel: 9,
                    desiredTone: 'Rubio claro',
                    desiredReflect: 'Platino',
                    coverage: 100,
                  },
                },
                advanced: {
                  contrast: 'medium',
                  direction: 'cooler',
                  graysCoverage: 100,
                  finalTexture: 'glossy',
                  specialNotes: '',
                },
                lifestyle: {
                  maintenanceLevel: MaintenanceLevel.MEDIUM,
                  avoidTones: [],
                  budgetLevel: BudgetLevel.STANDARD,
                },
                confidence: 50,
                isFromAI: false,
              };

              // Calcular viabilidad también para datos de ejemplo
              const diagnosis = {
                hairThickness: data.hairThickness,
                hairDensity: data.hairDensity,
                overallTone: data.overallTone,
                overallReflect: data.overallReflect || data.overallUndertone,
                averageLevel: data.zoneColorAnalysis?.[HairZone.ROOTS]?.level || 6,
                zoneAnalysis: data.zoneColorAnalysis,
                zonePhysicalAnalysis: data.zonePhysicalAnalysis,
              };

              const viabilityAnalysis = analyzeServiceViability(
                diagnosis,
                mockResult,
                data.zoneColorAnalysis
              );

              // Debug logging removed for production

              onUpdate({
                desiredAnalysisResult: mockResult,
                viabilityAnalysis: viabilityAnalysis,
              });
            },
          },
        ]
      );
    } finally {
      setIsAnalyzingDesired(false);
    }
  };

  // NEW: Colorimetry Guardian - Proactive validation
  const runColorimetryGuardian = (desiredResult: DesiredColorAnalysisResult) => {
    try {
      // Extract current hair state from diagnosis
      const currentLevel = data.zoneColorAnalysis?.[HairZone.ROOTS]?.level || 6;
      const currentState = data.zoneColorAnalysis?.[HairZone.ROOTS]?.artificialColor ? 'colored' : 'natural';

      // Extract desired level from analysis
      const desiredLevelStr = desiredResult?.general?.overallLevel || '7/0';
      const desiredLevel = parseInt(desiredLevelStr.split('/')[0]) || 7;

      // Determine hair condition from physical analysis
      let hairCondition: 'healthy' | 'damaged' | 'severely_damaged' = 'healthy';
      const rootsDamage = data.zonePhysicalAnalysis?.[HairZone.ROOTS]?.damage;
      const midsDamage = data.zonePhysicalAnalysis?.[HairZone.MIDS]?.damage;
      const endsDamage = data.zonePhysicalAnalysis?.[HairZone.ENDS]?.damage;

      if (rootsDamage === 'Alto' || midsDamage === 'Alto' || endsDamage === 'Alto') {
        hairCondition = 'severely_damaged';
      } else if (rootsDamage === 'Medio' || midsDamage === 'Medio' || endsDamage === 'Medio') {
        hairCondition = 'damaged';
      }

      // Build validation request
      const validationRequest: PreValidationRequest = {
        currentLevel,
        desiredLevel,
        currentState,
        currentTone: data.overallTone || 'natural',
        desiredTone: desiredResult?.general?.overallTone || 'natural',
        hairCondition,
        previousTreatments: [], // TODO: Extract from client history
        hasMetallicSalts: false, // TODO: Extract from diagnosis
        hasHenna: false, // TODO: Extract from diagnosis
        grayPercentage: data.zoneColorAnalysis?.[HairZone.ROOTS]?.grayPercentage || 0,
        brand: data.selectedBrand || 'Wella Professionals',
        line: data.selectedLine || 'Illumina Color',
      };

      // Run proactive validation
      const validation = ColorimetryGuardian.validateBeforeFormulation(validationRequest);

      // If there are violations, show the guardian dialog
      if (validation.violations.length > 0) {
        setGuardianValidation(validation);
        setShowGuardianDialog(true);

        // Haptic feedback for attention
        Haptics.notificationAsync(
          validation.riskLevel === 'critical'
            ? Haptics.NotificationFeedbackType.Error
            : Haptics.NotificationFeedbackType.Warning
        );

        logger.info('Colorimetry Guardian detected violations', 'DesiredColorStep', {
          violationCount: validation.violations.length,
          riskLevel: validation.riskLevel,
          canProceed: validation.canProceed
        });
      } else {
        logger.info('Colorimetry Guardian validation passed', 'DesiredColorStep', {
          approach: validation.recommendedApproach,
          sessions: validation.estimatedSessions
        });
      }

    } catch (error) {
      logger.error('Error running Colorimetry Guardian', 'DesiredColorStep', { error });
    }
  };

  const _handlePhotoCaptured = (uri: string, angle: string, quality: 'low' | 'medium' | 'high') => {
    const message = handleDesiredCameraCapture(
      uri,
      angle,
      quality,
      data.desiredPhotos,
      photos => onUpdate({ desiredPhotos: photos }),
      currentDesiredPhotoTypeRef.current
    );

    if (message) {
      // Debug logging removed for production
    }
  };

  // NEW: Guardian Dialog Handlers
  const handleGuardianProceed = () => {
    setShowGuardianDialog(false);
    setGuardianValidation(null);

    // Log that user proceeded despite warnings
    if (guardianValidation) {
      logger.info('User proceeded despite Guardian warnings', 'DesiredColorStep', {
        violationCount: guardianValidation.violations.length,
        riskLevel: guardianValidation.riskLevel
      });
    }

    // Continue to next step
    onNext();
  };

  const handleGuardianCancel = () => {
    setShowGuardianDialog(false);
    setGuardianValidation(null);

    // User decided to review diagnosis - stay on current step
    logger.info('User chose to review diagnosis after Guardian warnings', 'DesiredColorStep');
  };

  const handleGuardianAlternative = (approach: string) => {
    setShowGuardianDialog(false);
    setGuardianValidation(null);

    // Log selected alternative
    logger.info('User selected Guardian alternative', 'DesiredColorStep', { approach });

    // TODO: Implement alternative approach logic
    Alert.alert(
      'Alternativa Seleccionada',
      `Has elegido: ${approach}\n\nEsta funcionalidad se implementará en la siguiente fase.`,
      [{ text: 'Entendido' }]
    );
  };

  return (
    <>
      <AIResultNotification
        visible={showAINotification}
        onDismiss={() => setShowAINotification(false)}
        message="Análisis de color deseado completado"
        fieldsCount={aiFieldsCount}
        onViewResults={() => {
          if (scrollRef.current) {
            // Scroll to the analysis form with more noticeable offset
            scrollRef.current.scrollTo({ y: 600, animated: true });
            // Add haptic feedback
            setTimeout(() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            }, 300);
          }
        }}
      />

      <ScrollView ref={scrollRef} style={styles.container} showsVerticalScrollIndicator={false}>
        <View style={styles.stepContainer}>
          <BeautyCard variant="subtle" style={styles.headerCard}>
            <Text style={styles.stepTitle}>Resultado Deseado</Text>
            {data.client && (
              <Text style={styles.clientName}>Cliente: {data.client?.name || 'Cliente'}</Text>
            )}
          </BeautyCard>

          {/* Mostrar resumen del diagnóstico actual */}
          {data.hairThickness && (
            <DiagnosisSummary
              diagnosis={{
                thickness: data.hairThickness,
                density: data.hairDensity,
                tone: data.overallTone,
                reflect: data.overallReflect || data.overallUndertone,
                level: data.zoneColorAnalysis?.[HairZone.ROOTS]?.level,
                porosity: data.zonePhysicalAnalysis?.[HairZone.ROOTS]?.porosity,
                damage: data.zonePhysicalAnalysis?.[HairZone.ROOTS]?.damage,
                grayPercentage: data.zoneColorAnalysis?.[HairZone.ROOTS]?.grayPercentage,
              }}
            />
          )}

          {/* Method tabs */}
          <BeautyCard variant="default" style={styles.tabsContainer}>
            <TouchableOpacity
              style={[styles.tab, data.desiredMethod === 'ai' && styles.activeTab]}
              onPress={() => onUpdate({ desiredMethod: 'ai' })}
            >
              <Zap
                size={16}
                color={
                  data.desiredMethod === 'ai'
                    ? BeautyMinimalTheme.semantic.interactive.primary.default
                    : BeautyMinimalTheme.semantic.text.secondary
                }
              />
              <Text style={[styles.tabText, data.desiredMethod === 'ai' && styles.activeTabText]}>
                Con IA ✨
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.tab, data.desiredMethod === 'manual' && styles.activeTab]}
              onPress={() => handleManualModeSelect()}
            >
              <Eye
                size={16}
                color={
                  data.desiredMethod === 'manual'
                    ? BeautyMinimalTheme.semantic.interactive.primary.default
                    : BeautyMinimalTheme.semantic.text.secondary
                }
              />
              <Text
                style={[styles.tabText, data.desiredMethod === 'manual' && styles.activeTabText]}
              >
                Manual
              </Text>
            </TouchableOpacity>
          </BeautyCard>

          {/* Help text when AI data is present */}
          {data.desiredAnalysisResult?.isFromAI && data.desiredMethod === 'ai' && (
            <Text style={styles.helpText}>Los valores son editables • Toca para modificar</Text>
          )}

          {/* Photo capture section - Only show in AI mode */}
          {data.desiredMethod === 'ai' && (
            <>
              <Text style={styles.sectionTitle}>Referencias del color deseado (3-5 imágenes)</Text>

              <DesiredPhotoGallery
                photos={data.desiredPhotos}
                onAddPhoto={handleDesiredPhotoAdd}
                onCameraCapture={handleDesiredCameraNormal}
                onRemovePhoto={photoId => {
                  const updatedPhotos = data.desiredPhotos.filter(p => p.id !== photoId);
                  onUpdate({ desiredPhotos: updatedPhotos });
                }}
                maxPhotos={5}
              />

              {data.desiredPhotos.length > 0 && (
                <BeautyButton
                  title="Analizar con IA"
                  loadingTitle="Analizando..."
                  onPress={analyzeDesiredPhotos}
                  disabled={isAnalyzingDesired}
                  loading={isAnalyzingDesired}
                  variant="primary"
                  size="lg"
                  icon={Zap}
                  fullWidth
                  style={styles.analyzeButton}
                />
              )}
            </>
          )}

          {/* Show hair-themed loading animation during desired color analysis */}
          {isAnalyzingDesired && (
            <BeautyCard variant="default" style={styles.loadingCard}>
              <PhotoAnalysisLoading stage="analyzing-color" showProgressText={true} />
            </BeautyCard>
          )}

          {/* Professional confidence indicator for desired color analysis */}
          {data.desiredAnalysisResult &&
            typeof data.desiredAnalysisResult.confidence === 'number' &&
            data.desiredAnalysisResult.confidence > 0 &&
            !isAnalyzingDesired &&
            data.desiredAnalysisResult.isFromAI && (
              <ConfidenceIndicator
                confidence={data.desiredAnalysisResult.confidence}
                context="diagnosis"
                showDetailed={true}
                reasoning={[
                  'Referencias analizadas mediante IA',
                  `Técnica detectada: ${data.desiredAnalysisResult.general?.technique || 'No detectada'}`,
                  `Nivel objetivo: ${data.desiredAnalysisResult.general?.overallLevel || 'No especificado'}`,
                  `Tono deseado: ${data.desiredAnalysisResult.general?.overallTone || 'No especificado'}`,
                ]}
                analysisData={{
                  technique: data.desiredAnalysisResult.general?.technique,
                  level: data.desiredAnalysisResult.general?.overallLevel,
                  tone: data.desiredAnalysisResult.general?.overallTone,
                  confidence: data.desiredAnalysisResult.confidence,
                }}
              />
            )}

          {/* AI Status Indicator */}
          {data.desiredAnalysisResult && (
            <View
              style={[
                styles.aiStatusBadge,
                !data.desiredAnalysisResult.isFromAI && styles.aiStatusBadgeMock,
              ]}
            >
              {data.desiredAnalysisResult.isFromAI ? (
                <>
                  <Sparkles size={14} color={Colors.light.success} />
                  <Text style={styles.aiStatusText}>Análisis con IA</Text>
                </>
              ) : (
                <>
                  <AlertTriangle size={14} color={Colors.light.warning} />
                  <Text style={[styles.aiStatusText, styles.aiStatusTextMock]}>
                    Datos de ejemplo - Ajusta manualmente
                  </Text>
                </>
              )}
            </View>
          )}

          {/* Desired Color Analysis Form - Always visible */}
          <DesiredColorAnalysisForm
            analysisResult={data.desiredAnalysisResult}
            onAnalysisChange={newResult => {
              onUpdate({ desiredAnalysisResult: newResult });
            }}
            isFromAI={
              data.desiredMethod === 'ai' && (data.desiredAnalysisResult?.isFromAI || false)
            }
          />

          {/* Viability Analysis */}
          {data.desiredAnalysisResult && data.viabilityAnalysis && (
            <ViabilityIndicator
              analysis={data.viabilityAnalysis}
              loading={isRecalculatingViability}
            />
          )}

          {/* Continue Button */}
          {(data.desiredMethod === 'manual' || data.desiredAnalysisResult) && (
            <BeautyButton
              title="Continuar a la Formulación"
              onPress={onNext}
              variant="primary"
              size="lg"
              fullWidth
              style={styles.continueButton}
            />
          )}
        </View>
      </ScrollView>

      {/* NEW: Colorimetry Guardian Dialog */}
      {guardianValidation && (
        <ColorimetryGuardianDialog
          visible={showGuardianDialog}
          validationResult={guardianValidation}
          onProceed={handleGuardianProceed}
          onCancel={handleGuardianCancel}
          onSelectAlternative={handleGuardianAlternative}
        />
      )}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  stepContainer: {
    padding: BeautyMinimalTheme.spacing.component.screenMargin,
  },
  headerCard: {
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  stepTitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.title,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: BeautyMinimalTheme.spacing.xs,
  },
  clientName: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    color: BeautyMinimalTheme.semantic.text.secondary,
    marginBottom: BeautyMinimalTheme.spacing.sm,
  },
  tabsContainer: {
    flexDirection: 'row',
    padding: BeautyMinimalTheme.spacing.xs,
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  tab: {
    flex: 1,
    paddingVertical: BeautyMinimalTheme.spacing.md,
    paddingHorizontal: BeautyMinimalTheme.spacing.lg,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    gap: BeautyMinimalTheme.spacing.sm,
    borderRadius: BeautyMinimalTheme.radius.md,
  },
  activeTab: {
    backgroundColor: BeautyMinimalTheme.semantic.background.primary,
    ...BeautyMinimalTheme.shadows.soft,
  },
  tabText: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
    color: BeautyMinimalTheme.semantic.text.secondary,
  },
  activeTabText: {
    color: BeautyMinimalTheme.semantic.interactive.primary.default,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
  },
  helpText: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    color: BeautyMinimalTheme.semantic.text.tertiary,
    textAlign: 'center',
    marginTop: -BeautyMinimalTheme.spacing.md,
    marginBottom: BeautyMinimalTheme.spacing.lg,
    fontStyle: 'italic',
  },
  sectionTitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.subheading,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  analyzeButton: {
    marginTop: BeautyMinimalTheme.spacing.lg,
  },
  aiStatusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    backgroundColor: Colors.light.success + '10',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginBottom: 12,
    alignSelf: 'flex-start',
  },
  aiStatusBadgeMock: {
    backgroundColor: Colors.light.warning + '10',
  },
  aiStatusText: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.light.success,
  },
  aiStatusTextMock: {
    color: Colors.light.warning,
  },
  continueButton: {
    marginTop: BeautyMinimalTheme.spacing.lg,
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  loadingCard: {
    marginVertical: 12,
    padding: 16,
  },
});
