import React, { useRef, useEffect, useState } from 'react';
import { logger } from '@/utils/logger';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Switch,
  TextInput,
  Alert,
} from 'react-native';
import { ChevronRight, Zap } from 'lucide-react-native';
import * as Haptics from 'expo-haptics';
import { BeautyCard, BeautyButton } from '@/components/beauty';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { getTypographyStyle } from '@/constants/typography-system';
import Colors from '@/constants/colors';
import { hairColorMap } from '@/constants/hair-colors';
import { getToneNameFromLevel } from '@/utils/professional-colorimetry';

// Color constants for fallback values
const FALLBACK_COLORS = {
  primary: '#5c3825', // Castaño medio neutral en lugar de amethyst (rosa)
  secondary: '#d4a574', // Rubio medio neutral en lugar de cloud
};
// Helper functions to get hair colors from analysis results
interface AnalysisResult {
  overallTone?: string;
  zones?: {
    [zoneName: string]: {
      tone?: string;
      level?: number;
      [key: string]: unknown;
    };
  };
  [key: string]: unknown;
}

const getHairColorFromAnalysis = (analysisResult: AnalysisResult): string | null => {
  if (!analysisResult) return null;

  const level = analysisResult.averageLevel || analysisResult.averageDepthLevel || 5;
  const tone = analysisResult.overallTone;

  // First try to use the exact tone name if it exists
  if (tone && hairColorMap[tone]) {
    return hairColorMap[tone];
  }

  // If tone is generic (like "Castaño"), use level to get specific tone
  const specificToneName = getToneNameFromLevel(level);
  if (specificToneName && hairColorMap[specificToneName]) {
    return hairColorMap[specificToneName];
  }

  // Fallback to generic tone name
  if (tone && hairColorMap[tone]) {
    return hairColorMap[tone];
  }

  return null;
};

interface DesiredResult {
  desiredAnalysisResult?: {
    detectedTone?: string;
    [key: string]: unknown;
  };
  [key: string]: unknown;
}

const getHairColorFromDesired = (desiredResult: DesiredResult): string | null => {
  if (!desiredResult?.general) return null;

  const level = desiredResult.general.overallLevel || 8;
  const tone = desiredResult.general.overallTone;

  // First try to use the exact tone name if it exists
  if (tone && hairColorMap[tone]) {
    return hairColorMap[tone];
  }

  // If tone is generic, use level to get specific tone
  const specificToneName = getToneNameFromLevel(level);
  if (specificToneName && hairColorMap[specificToneName]) {
    return hairColorMap[specificToneName];
  }

  // Fallback to generic tone name
  if (tone && hairColorMap[tone]) {
    return hairColorMap[tone];
  }

  return null;
};
import { ServiceData } from '@/src/service/hooks/useServiceFlow';
import { useFormulation } from '@/src/service/hooks/useFormulation';
import { useSalonConfigStore } from '@/stores/salon-config-store';

// Import existing components
import ViabilityIndicator from '@/components/ViabilityIndicator';
import FormulaCostBreakdown from '@/components/FormulaCostBreakdown';
import { BrandSelectionModal } from '@/components/BrandSelectionModal';
import AIResultNotification from '@/components/AIResultNotification';

// Import new enhanced components
import { MaterialsSummaryCard } from '@/components/formulation/MaterialsSummaryCard';
import { QuickAdjustPanel, AdjustmentType } from '@/components/formulation/QuickAdjustPanel';
import FormulaTips from '@/components/formulation/FormulaTips';
import HairRecommendations from '@/components/HairRecommendations';
import BrandDossierCard from '@/components/formulation/BrandDossierCard';
import { useBrandDossiers } from '@/hooks/useBrandDossiers';

// Import specialized display components
import { GlobalFormulaDisplay, ZonalFormulaDisplay } from '@/src/service/components/displays';

interface FormulationStepProps {
  data: ServiceData;
  onUpdate: (updates: Partial<ServiceData>) => void;
  onNext: () => void;
  onBack: () => void;
  onSave?: () => void;
  onSaveSilent?: () => void;
  analysisResult?: AnalysisResult; // AI analysis result from diagnosis
}

export const FormulationStep: React.FC<FormulationStepProps> = ({
  data,
  onUpdate,
  onNext,
  onBack: _onBack,
  onSave,
  onSaveSilent,
  analysisResult,
}) => {
  // ScrollView ref for auto-scroll
  const scrollRef = useRef<ScrollView>(null);
  const [showAINotification, setShowAINotification] = useState(false);
  const [aiFieldsCount, setAIFieldsCount] = useState(0);
  const hasShownNotificationRef = useRef(false);

  const {
    selectedBrand,
    setSelectedBrand,
    selectedLine,
    setSelectedLine,
    formula,
    setFormula,
    isFormulaFromAI,
    setIsFormulaFromAI,
    isGeneratingFormula,
    showBrandModal,
    setShowBrandModal,
    brandModalType,
    setBrandModalType,
    conversionMode,
    setConversionMode,
    originalBrand,
    setOriginalBrand,
    originalLine,
    setOriginalLine,
    originalFormula,
    setOriginalFormula,
    formulaCost,
    viabilityAnalysis,
    generateFormulaWithAI,
    analyzeViability,
    formulationData,
  } = useFormulation();

  // Brand dossiers hook
  const { getBrandDossier, getLineDossier, hasDossierData } = useBrandDossiers();
  const [isDossierExpanded, setIsDossierExpanded] = useState(false);

  // Initialize state from parent data only once
  React.useEffect(() => {
    if (!selectedBrand && data.selectedBrand) {
      setSelectedBrand(data.selectedBrand);
    }
    if (!selectedLine && data.selectedLine) {
      setSelectedLine(data.selectedLine);
    }
    if (!formula && data.formula) {
      setFormula(data.formula);
      setIsFormulaFromAI(data.isFormulaFromAI);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Empty dependency array - only run once

  // Update parent when specific state changes (avoiding infinite loops)
  React.useEffect(() => {
    if (formula || formulaCost || viabilityAnalysis || formulationData) {
      onUpdate({
        selectedBrand,
        selectedLine,
        formula,
        formulationData,
        isFormulaFromAI,
        formulaCost,
        viabilityAnalysis,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    formula,
    formulaCost,
    viabilityAnalysis,
    formulationData,
    selectedBrand,
    selectedLine,
    isFormulaFromAI,
    // onUpdate removed from deps - parent function changes on every render
  ]);

  // Analyze viability when desired analysis changes
  React.useEffect(() => {
    if (data.desiredAnalysisResult && analysisResult) {
      const viability = analyzeViability(
        analysisResult,
        data.desiredAnalysisResult,
        data.zoneColorAnalysis
      );
      onUpdate({ viabilityAnalysis: viability });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    data.desiredAnalysisResult,
    analysisResult,
    data.zoneColorAnalysis,
    analyzeViability,
    // onUpdate removed from deps - parent function changes on every render
  ]);

  // Show notification and scroll when formula is generated
  useEffect(() => {
    if ((formula || formulationData) && scrollRef.current && !hasShownNotificationRef.current) {
      // Count what was generated
      let fieldsCount = 0;
      if (formula) fieldsCount++;
      if (formulationData) {
        if (formulationData.products?.length) fieldsCount += formulationData.products.length;
        if (formulationData.steps?.length) fieldsCount += formulationData.steps.length;
        if (formulationData.techniques?.length) fieldsCount += formulationData.techniques.length;
      }

      setAIFieldsCount(fieldsCount);
      setShowAINotification(true);
      hasShownNotificationRef.current = true;

      // Haptic feedback
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

      // Smooth scroll to formula section
      setTimeout(() => {
        scrollRef.current?.scrollTo({ y: 400, animated: true });
      }, 300);
    }

    // Reset the flag when formula is cleared
    if (!formula && !formulationData) {
      hasShownNotificationRef.current = false;
    }
  }, [formula, formulationData]);

  const handleGenerateFormula = async (adjustment?: AdjustmentType) => {
    try {
      // If there's an adjustment, include it in the AI request
      let adjustmentContext = '';
      if (adjustment) {
        if (adjustment.type === 'temperature') {
          adjustmentContext = `\nAjuste solicitado: Hacer la fórmula más ${adjustment.value === 'cooler' ? 'fría' : 'cálida'}`;
        } else if (adjustment.type === 'brightness') {
          adjustmentContext = `\nAjuste solicitado: Hacer la fórmula más ${adjustment.value === 'lighter' ? 'clara' : 'oscura'}`;
        } else if (adjustment.type === 'oxidant') {
          adjustmentContext = `\nAjuste solicitado: ${adjustment.value === 'increase' ? 'Aumentar' : 'Disminuir'} el volumen del oxidante`;
        } else if (adjustment.type === 'custom') {
          adjustmentContext = `\nAjuste solicitado: ${adjustment.value}`;
        } else if (adjustment.type === 'brand') {
          // Brand change is handled through existing state
        }
      }

      const message = await generateFormulaWithAI(
        analysisResult,
        data.desiredAnalysisResult,
        data.zoneColorAnalysis,
        data.clientId || undefined,
        adjustmentContext
      );

      // Save silently when formula is generated
      onSaveSilent?.();

      if (message) {
        // Debug logging removed for production
      }
    } catch (error) {
      logger.error('Error generating formula:', error);
    }
  };

  const handleAdjustFormula = async (adjustment: AdjustmentType) => {
    if (adjustment.type === 'brand') {
      // Brand change is handled separately
      return;
    }

    // Regenerate formula with adjustment
    await handleGenerateFormula(adjustment);
  };

  const handleBrandSelection = (brand: string, line: string) => {
    if (brandModalType === 'main') {
      setSelectedBrand(brand);
      setSelectedLine(line);
      onSave?.();

      // If in conversion mode and target brand equals source brand, clear source
      if (conversionMode && brand === originalBrand) {
        setOriginalBrand('');
        setOriginalLine('');
        Alert.alert('Atención', 'La marca de destino no puede ser igual a la marca de origen');
      }
    } else {
      // Conversion mode - setting original brand
      if (brand === selectedBrand) {
        Alert.alert('Atención', 'La marca de origen debe ser diferente a tu marca destino');
        return;
      }
      setOriginalBrand(brand);
      setOriginalLine(line);
    }
    setShowBrandModal(false);
  };

  // Adaptive formula rendering based on selected technique
  const renderFormulationUI = () => {
    const technique = data.desiredAnalysisResult?.general?.technique;

    if (!formula) {
      return null; // No formula to display yet
    }

    // Common props for all display components
    const commonProps = {
      formula,
      selectedBrand,
      selectedLine,
      technique: technique || 'full_color',
      formulationData,
      clientName: data.client?.name,
      isFromAI: isFormulaFromAI,
      onEdit: setFormula,
      viabilityAnalysis,
      currentLevel: analysisResult?.averageLevel || analysisResult?.averageDepthLevel || 5,
      targetLevel:
        parseInt(data.desiredAnalysisResult?.general?.overallLevel?.split('/')[0] || '7') || 7,
    };

    switch (technique) {
      case 'full_color':
      case 'ombre':
      case 'color_correction':
      case 'money_piece':
        return <GlobalFormulaDisplay {...commonProps} />;

      case 'balayage':
      case 'highlights':
      case 'foilyage':
      case 'babylights':
      case 'chunky_highlights':
      case 'reverse_balayage':
        return <ZonalFormulaDisplay {...commonProps} />;

      default:
        // Fallback to zonal display for unknown techniques
        return <ZonalFormulaDisplay {...commonProps} />;
    }
  };

  return (
    <>
      <AIResultNotification
        visible={showAINotification}
        onDismiss={() => setShowAINotification(false)}
        message="Fórmula generada con IA"
        fieldsCount={aiFieldsCount}
        onViewResults={() => {
          if (scrollRef.current) {
            // Scroll to formula display with more noticeable offset
            scrollRef.current.scrollTo({ y: 500, animated: true });
            // Add haptic feedback
            setTimeout(() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            }, 300);
          }
        }}
      />

      <ScrollView ref={scrollRef} style={styles.container} showsVerticalScrollIndicator={false}>
        <View style={styles.stepContainer}>
          <BeautyCard variant="subtle" style={styles.headerCard}>
            <Text style={styles.stepTitle}>Generación de Fórmula</Text>
            {data.client && (
              <Text style={styles.clientName}>Cliente: {data.client?.name || 'Cliente'}</Text>
            )}
          </BeautyCard>

          {/* Color Transition Summary */}
          {analysisResult && data.desiredAnalysisResult && (
            <BeautyCard variant="default" style={styles.colorTransitionCard}>
              <View style={styles.colorTransitionHeader}>
                <Text style={styles.colorTransitionTitle}>Transformación de Color</Text>
              </View>
              <View style={styles.colorTransitionContent}>
                <View style={styles.colorBox}>
                  <View
                    style={[
                      styles.colorSample,
                      {
                        backgroundColor:
                          getHairColorFromAnalysis(analysisResult) || FALLBACK_COLORS.primary,
                      },
                    ]}
                  />
                  <Text style={styles.colorLevel}>
                    Nivel {analysisResult.averageLevel || analysisResult.averageDepthLevel || 5}
                  </Text>
                  <Text style={styles.colorTone}>{analysisResult.overallTone || 'Castaño'}</Text>
                </View>
                <ChevronRight size={24} color={Colors.light.primary} style={styles.arrowIcon} />
                <View style={styles.colorBox}>
                  <View
                    style={[
                      styles.colorSample,
                      {
                        backgroundColor:
                          getHairColorFromDesired(data.desiredAnalysisResult) ||
                          FALLBACK_COLORS.secondary,
                      },
                    ]}
                  />
                  <Text style={styles.colorLevel}>
                    Nivel {data.desiredAnalysisResult.general.overallLevel || 8}
                  </Text>
                  <Text style={styles.colorTone}>
                    {data.desiredAnalysisResult.general.overallTone || 'Rubio'}
                  </Text>
                </View>
              </View>
              {data.desiredAnalysisResult.general.technique && (
                <View style={styles.techniqueIndicator}>
                  <Text style={styles.techniqueLabel}>
                    Técnica: {data.desiredAnalysisResult.general.technique}
                  </Text>
                </View>
              )}
            </BeautyCard>
          )}

          <BeautyCard variant="default" style={styles.formGroup}>
            <Text style={styles.label}>Marca</Text>
            <TouchableOpacity
              style={styles.selectContainer}
              onPress={() => {
                setBrandModalType('main');
                setShowBrandModal(true);
              }}
            >
              <Text style={styles.selectText}>{selectedBrand}</Text>
              <ChevronRight size={16} color={Colors.light.gray} />
            </TouchableOpacity>
          </BeautyCard>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Línea</Text>
            <TouchableOpacity
              style={styles.selectContainer}
              onPress={() => {
                setBrandModalType('main');
                setShowBrandModal(true);
              }}
            >
              <Text style={styles.selectText}>{selectedLine}</Text>
              <ChevronRight size={16} color={Colors.light.gray} />
            </TouchableOpacity>
          </View>

          {/* Brand Dossier Card */}
          {selectedBrand && selectedLine && hasDossierData(selectedBrand, selectedLine) && (
            <BrandDossierCard
              brandName={selectedBrand}
              lineName={selectedLine}
              brandDossier={getBrandDossier(selectedBrand)}
              lineDossier={getLineDossier(selectedBrand, selectedLine)}
              isExpanded={isDossierExpanded}
              onToggle={() => setIsDossierExpanded(!isDossierExpanded)}
            />
          )}

          {/* Brand Conversion Section */}
          <View
            style={[styles.conversionSection, conversionMode && styles.conversionSectionActive]}
          >
            <TouchableOpacity
              style={styles.conversionHeader}
              activeOpacity={0.7}
              onPress={() => setConversionMode(!conversionMode)}
            >
              <View style={styles.conversionTitleContainer}>
                <Text style={styles.conversionTitle}>Adaptar fórmula de otra marca</Text>
                <Text style={styles.conversionSubtitle}>Obtén el equivalente en tu marca</Text>
              </View>
              <Switch
                trackColor={{
                  false: Colors.light.lightGray,
                  true: Colors.light.primary,
                }}
                thumbColor={conversionMode ? Colors.light.primary : Colors.light.gray}
                ios_backgroundColor={Colors.light.lightGray}
                onValueChange={setConversionMode}
                value={conversionMode}
                style={styles.conversionSwitch}
              />
            </TouchableOpacity>

            {conversionMode && (
              <BeautyCard variant="default" style={styles.conversionContent}>
                {/* Formula Input First */}
                <View style={styles.formGroup}>
                  <Text style={styles.label}>¿Cuál es la fórmula?</Text>
                  <TextInput
                    style={[styles.input, styles.textArea, styles.conversionFormulaInput]}
                    value={originalFormula}
                    onChangeText={setOriginalFormula}
                    placeholder="Ej: 7.31 + 8.34 (2:1) • Oxidante 20 vol • 35 min"
                    multiline
                    numberOfLines={3}
                    textAlignVertical="top"
                  />
                </View>

                <TouchableOpacity
                  style={styles.conversionBrandContainer}
                  onPress={() => {
                    setBrandModalType('conversion');
                    setShowBrandModal(true);
                  }}
                >
                  <Text style={styles.conversionLabel}>¿De qué marca es la fórmula?</Text>
                  <View style={styles.conversionSelectContainer}>
                    <Text style={styles.selectText}>{originalBrand || 'Seleccionar marca...'}</Text>
                    <ChevronRight size={16} color={Colors.light.gray} />
                  </View>
                </TouchableOpacity>

                {originalBrand && (
                  <TouchableOpacity
                    style={styles.conversionBrandContainer}
                    onPress={() => {
                      setBrandModalType('conversion');
                      setShowBrandModal(true);
                    }}
                  >
                    <Text style={styles.conversionLabel}>¿Qué línea o producto?</Text>
                    <View style={styles.conversionSelectContainer}>
                      <Text style={styles.selectText}>
                        {originalLine || 'Seleccionar línea...'}
                      </Text>
                      <ChevronRight size={16} color={Colors.light.gray} />
                    </View>
                  </TouchableOpacity>
                )}

                {originalBrand && originalLine && (
                  <View style={styles.conversionInfoBox}>
                    <Text style={styles.conversionInfoText}>
                      ✨ Se adaptará a {selectedBrand} {selectedLine}
                    </Text>
                    <Text style={styles.conversionInfoSubtext}>
                      Manteniendo el mismo resultado de color
                    </Text>
                  </View>
                )}
              </BeautyCard>
            )}

            <BeautyButton
              title={
                conversionMode && originalBrand && originalLine
                  ? 'Convertir y Generar Fórmula'
                  : 'Calcular Fórmula Personalizada'
              }
              onPress={() => handleGenerateFormula()}
              disabled={isGeneratingFormula}
              loading={isGeneratingFormula}
              variant="primary"
              size="lg"
              icon={Zap}
              fullWidth
              style={styles.aiButton}
            />
          </View>

          {/* Materials Summary Card - Shows required products immediately after formula generation */}
          {formula && (
            <MaterialsSummaryCard
              formulationData={formulationData}
              formulaText={formula}
              selectedBrand={selectedBrand}
              selectedLine={selectedLine}
            />
          )}

          {/* Quick Adjust Panel - Always visible after formula generation */}
          {formula && (
            <QuickAdjustPanel
              onAdjustFormula={handleAdjustFormula}
              onChangeBrand={(brand, line) => {
                setSelectedBrand(brand);
                setSelectedLine(line);
                handleGenerateFormula({ type: 'brand', brand, line });
              }}
              currentBrand={selectedBrand}
              currentLine={selectedLine}
              isGenerating={isGeneratingFormula}
            />
          )}

          {/* Adaptive Formulation UI - Renders appropriate component based on technique */}
          {renderFormulationUI()}

          {/* Intelligent Tips and Recommendations - Only show after formula is generated */}
          {formula && analysisResult && data.desiredAnalysisResult && (
            <>
              <FormulaTips
                analysis={analysisResult}
                technique={data.desiredAnalysisResult.general?.technique || 'full_color'}
                targetLevel={
                  parseInt(
                    data.desiredAnalysisResult?.general?.overallLevel?.split('/')[0] || '7'
                  ) || 7
                }
                viabilityAnalysis={data.viabilityAnalysis}
              />

              <HairRecommendations
                analysis={analysisResult}
                desiredAnalysis={data.desiredAnalysisResult}
              />
            </>
          )}

          {/* Viability Analysis */}
          {viabilityAnalysis && <ViabilityIndicator analysis={viabilityAnalysis} />}

          {/* Formula Cost Breakdown - Clean Production Version */}
          {formulaCost && (
            <FormulaCostBreakdown
              cost={formulaCost}
              isRealCost={
                formulaCost.hasAllRealCosts === true &&
                useSalonConfigStore.getState().configuration.inventoryControlLevel !==
                  'solo-formulas'
              }
            />
          )}

          {/* Continue Button */}
          {formula && (
            <BeautyButton
              title="Continuar al Resultado Final"
              onPress={onNext}
              variant="primary"
              size="lg"
              fullWidth
              style={styles.continueButton}
            />
          )}

          {/* Brand Selection Modal */}
          <BrandSelectionModal
            visible={showBrandModal}
            onClose={() => setShowBrandModal(false)}
            onSelectBrand={handleBrandSelection}
            currentBrand={brandModalType === 'main' ? selectedBrand : originalBrand}
            currentLine={brandModalType === 'main' ? selectedLine : originalLine}
            title={
              brandModalType === 'main' ? 'Seleccionar Marca y Línea' : 'Marca y Línea Original'
            }
            isConversionMode={brandModalType === 'conversion'}
            sourceBrand={brandModalType === 'conversion' ? originalBrand : undefined}
            sourceLine={brandModalType === 'conversion' ? originalLine : undefined}
          />
        </View>
      </ScrollView>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  stepContainer: {
    padding: BeautyMinimalTheme.spacing.component.screenMargin,
  },
  headerCard: {
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  stepTitle: {
    ...getTypographyStyle('sectionTitle'),
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: BeautyMinimalTheme.spacing.xs,
  },
  clientName: {
    ...getTypographyStyle('body'),
    color: BeautyMinimalTheme.semantic.text.secondary,
    marginBottom: BeautyMinimalTheme.spacing.sm,
  },
  colorTransitionCard: {
    marginBottom: BeautyMinimalTheme.spacing.lg,
    marginBottom: 20,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  colorTransitionHeader: {
    marginBottom: 16,
  },
  colorTransitionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.text,
  },
  colorTransitionContent: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  colorBox: {
    alignItems: 'center',
  },
  colorSample: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginBottom: 8,
    borderWidth: 3,
    borderColor: Colors.light.border,
  },
  colorLevel: {
    fontSize: 16,
    fontWeight: '700',
    color: Colors.light.text,
  },
  colorTone: {
    fontSize: 14,
    color: Colors.light.gray,
    marginTop: 2,
  },
  arrowIcon: {
    marginHorizontal: 20,
  },
  techniqueIndicator: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
  },
  techniqueLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.primary,
    textAlign: 'center',
  },
  formGroup: {
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  label: {
    ...getTypographyStyle('bodyEmphasis'),
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: BeautyMinimalTheme.spacing.sm,
  },
  selectContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: Colors.light.background,
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    minHeight: 48,
  },
  selectText: {
    fontSize: 15,
  },
  input: {
    backgroundColor: Colors.light.background,
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 15,
  },
  textArea: {
    minHeight: 100,
    paddingTop: 12,
    textAlignVertical: 'top',
  },
  conversionSection: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  conversionSectionActive: {
    borderColor: Colors.light.primary,
    borderWidth: 2,
  },
  conversionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    minHeight: 60,
  },
  conversionTitleContainer: {
    flex: 1,
    marginRight: 12,
  },
  conversionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
  },
  conversionSubtitle: {
    fontSize: 13,
    color: Colors.light.gray,
    marginTop: 2,
  },
  conversionSwitch: {
    transform: [{ scale: 0.8 }],
  },
  conversionContent: {
    marginTop: 8,
  },
  conversionBrandContainer: {
    marginBottom: 16,
  },
  conversionLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.light.text,
    marginBottom: 8,
  },
  conversionSelectContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: Colors.light.background,
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    minHeight: 48,
  },
  conversionFormulaInput: {
    minHeight: 140,
    paddingTop: 12,
  },
  conversionInfoBox: {
    backgroundColor: Colors.light.primary + '10',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: Colors.light.primary + '20',
  },
  conversionInfoText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.primary,
    marginBottom: 4,
  },
  conversionInfoSubtext: {
    fontSize: 13,
    color: Colors.light.text,
    lineHeight: 18,
  },
  aiButton: {
    marginBottom: BeautyMinimalTheme.spacing.lg,
    shadowRadius: 8,
    elevation: 4,
  },
  continueButton: {
    marginTop: BeautyMinimalTheme.spacing.lg,
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
});
