/**
 * useColorimetryGuardian Hook
 * Simplifies integration of the Colorimetry Guardian across components
 * Provides consistent validation and dialog management
 */

import { useState, useCallback } from 'react';
import { Alert } from 'react-native';
import * as Haptics from 'expo-haptics';
import { logger } from '@/utils/logger';

import { 
  ColorimetryGuardian, 
  type PreValidationRequest,
  type PreValidationResult 
} from '@/supabase/functions/salonier-assistant/utils/formula-validator';

export interface UseColorimetryGuardianOptions {
  onProceed?: () => void;
  onCancel?: () => void;
  onAlternativeSelected?: (approach: string) => void;
  autoShowDialog?: boolean;
}

export const useColorimetryGuardian = (options: UseColorimetryGuardianOptions = {}) => {
  const [showDialog, setShowDialog] = useState(false);
  const [validationResult, setValidationResult] = useState<PreValidationResult | null>(null);
  const [isValidating, setIsValidating] = useState(false);

  const {
    onProceed,
    onCancel,
    onAlternativeSelected,
    autoShowDialog = true
  } = options;

  /**
   * Run proactive validation
   */
  const validateColorProcess = useCallback(async (request: PreValidationRequest): Promise<PreValidationResult> => {
    setIsValidating(true);
    
    try {
      const result = ColorimetryGuardian.validateBeforeFormulation(request);
      setValidationResult(result);
      
      // Auto-show dialog if there are violations and autoShowDialog is enabled
      if (result.violations.length > 0 && autoShowDialog) {
        setShowDialog(true);
        
        // Haptic feedback based on risk level
        const hapticType = result.riskLevel === 'critical' 
          ? Haptics.NotificationFeedbackType.Error
          : Haptics.NotificationFeedbackType.Warning;
        
        try {
          await Haptics.notificationAsync(hapticType);
        } catch {
          // Silently fail if haptics not available
        }
      }
      
      logger.info('Colorimetry Guardian validation completed', 'useColorimetryGuardian', {
        violationCount: result.violations.length,
        riskLevel: result.riskLevel,
        canProceed: result.canProceed,
        approach: result.recommendedApproach
      });
      
      return result;
    } catch (error) {
      logger.error('Error in Colorimetry Guardian validation', 'useColorimetryGuardian', { error });
      
      // Return a safe fallback result
      const fallbackResult: PreValidationResult = {
        canProceed: false,
        violations: [{
          type: 'critical',
          rule: 'validation_error',
          message: 'Error en validación de seguridad',
          conversationalMessage: 'Ha ocurrido un error al validar la seguridad del proceso. Por precaución, te recomiendo revisar manualmente antes de proceder.',
          suggestedAction: 'Revisar diagnóstico y configuración',
          canProceed: false,
          requiresConfirmation: true
        }],
        recommendedApproach: 'abort',
        estimatedSessions: 1,
        riskLevel: 'critical',
        conversationalSummary: 'Error en validación - se requiere revisión manual por seguridad.'
      };
      
      setValidationResult(fallbackResult);
      if (autoShowDialog) {
        setShowDialog(true);
      }
      
      return fallbackResult;
    } finally {
      setIsValidating(false);
    }
  }, [autoShowDialog]);

  /**
   * Handle dialog actions
   */
  const handleProceed = useCallback(() => {
    setShowDialog(false);
    
    if (validationResult) {
      logger.info('User proceeded despite Guardian warnings', 'useColorimetryGuardian', {
        violationCount: validationResult.violations.length,
        riskLevel: validationResult.riskLevel
      });
    }
    
    onProceed?.();
  }, [validationResult, onProceed]);

  const handleCancel = useCallback(() => {
    setShowDialog(false);
    
    logger.info('User cancelled after Guardian warnings', 'useColorimetryGuardian');
    onCancel?.();
  }, [onCancel]);

  const handleAlternativeSelected = useCallback((approach: string) => {
    setShowDialog(false);
    
    logger.info('User selected Guardian alternative', 'useColorimetryGuardian', { approach });
    onAlternativeSelected?.(approach);
  }, [onAlternativeSelected]);

  /**
   * Manual dialog control
   */
  const showGuardianDialog = useCallback(() => {
    if (validationResult) {
      setShowDialog(true);
    }
  }, [validationResult]);

  const hideGuardianDialog = useCallback(() => {
    setShowDialog(false);
  }, []);

  /**
   * Quick validation helpers
   */
  const hasViolations = validationResult?.violations.length > 0;
  const hasCriticalViolations = validationResult?.violations.some(v => v.type === 'critical') || false;
  const canProceedSafely = validationResult?.canProceed || false;
  const riskLevel = validationResult?.riskLevel || 'low';

  /**
   * Get violation summary for UI display
   */
  const getViolationSummary = useCallback(() => {
    if (!validationResult || validationResult.violations.length === 0) {
      return null;
    }

    const criticalCount = validationResult.violations.filter(v => v.type === 'critical').length;
    const warningCount = validationResult.violations.filter(v => v.type === 'warning').length;

    return {
      total: validationResult.violations.length,
      critical: criticalCount,
      warnings: warningCount,
      summary: validationResult.conversationalSummary,
      approach: validationResult.recommendedApproach,
      sessions: validationResult.estimatedSessions
    };
  }, [validationResult]);

  return {
    // State
    showDialog,
    validationResult,
    isValidating,
    
    // Actions
    validateColorProcess,
    handleProceed,
    handleCancel,
    handleAlternativeSelected,
    showGuardianDialog,
    hideGuardianDialog,
    
    // Computed values
    hasViolations,
    hasCriticalViolations,
    canProceedSafely,
    riskLevel,
    getViolationSummary,
  };
};

/**
 * Helper function to build validation request from service data
 */
export const buildValidationRequest = (
  serviceData: any, // TODO: Type this properly with ServiceData
  desiredResult: any // TODO: Type this properly with DesiredColorAnalysisResult
): PreValidationRequest => {
  // Extract current hair state
  const currentLevel = serviceData.zoneColorAnalysis?.roots?.level || 6;
  const currentState = serviceData.zoneColorAnalysis?.roots?.artificialColor ? 'colored' : 'natural';
  
  // Extract desired level
  const desiredLevelStr = desiredResult?.general?.overallLevel || '7/0';
  const desiredLevel = parseInt(desiredLevelStr.split('/')[0]) || 7;
  
  // Determine hair condition
  let hairCondition: 'healthy' | 'damaged' | 'severely_damaged' = 'healthy';
  const damages = [
    serviceData.zonePhysicalAnalysis?.roots?.damage,
    serviceData.zonePhysicalAnalysis?.mids?.damage,
    serviceData.zonePhysicalAnalysis?.ends?.damage
  ];
  
  if (damages.some(d => d === 'Alto')) {
    hairCondition = 'severely_damaged';
  } else if (damages.some(d => d === 'Medio')) {
    hairCondition = 'damaged';
  }
  
  return {
    currentLevel,
    desiredLevel,
    currentState,
    currentTone: serviceData.overallTone || 'natural',
    desiredTone: desiredResult?.general?.overallTone || 'natural',
    hairCondition,
    previousTreatments: [], // TODO: Extract from client history
    hasMetallicSalts: false, // TODO: Extract from diagnosis
    hasHenna: false, // TODO: Extract from diagnosis
    grayPercentage: serviceData.zoneColorAnalysis?.roots?.grayPercentage || 0,
    brand: serviceData.selectedBrand || 'Wella Professionals',
    line: serviceData.selectedLine || 'Illumina Color',
  };
};
