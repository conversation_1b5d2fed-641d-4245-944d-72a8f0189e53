import { useState, useCallback } from 'react';
import { Alert } from 'react-native';
import { router } from 'expo-router';
import { HairZone, ZoneColorAnalysis, ZonePhysicalAnalysis } from '@/types/hair-diagnosis';
import { CapturedPhoto } from '@/types/photo-capture';
import { DesiredPhoto } from '@/types/desired-photo';
import { DesiredColorAnalysisResult } from '@/types/desired-analysis';
import { ViabilityAnalysis, FormulaCost } from '@/types/formulation';
import { Client } from '@/stores/client-store';
import { useSmartTransitions } from '@/hooks/useSmartTransitions';
import { SmartViabilityAnalysis } from '@/utils/viability-analyzer';

// Define the steps of the consultation flow
export const STEPS = [
  { id: 'diagnosis', title: 'Diagnóstico Capilar' },
  { id: 'desired', title: 'Resultado Deseado' },
  { id: 'formulation', title: 'Formulación' },
  { id: 'result', title: 'Resultado Final' },
];

export interface ServiceData {
  // Client info
  client: Client | null;
  clientId: string | null;

  // Diagnosis data
  diagnosisMethod: string;
  hairPhotos: CapturedPhoto[];
  diagnosisImage?: string; // Main diagnosis image URL
  hairThickness: string;
  hairDensity: string;
  overallTone: string;
  overallReflect: string;
  overallUndertone?: string; // Mantener para compatibilidad
  lastChemicalProcessType: string;
  lastChemicalProcessDate: string;
  hasUsedHomeRemedies: boolean;
  hairLength: number;
  monthlyGrowth: number;
  diagnosisNotes: string;
  zoneColorAnalysis: Record<HairZone, Partial<ZoneColorAnalysis>>;
  zonePhysicalAnalysis: Record<HairZone, Partial<ZonePhysicalAnalysis>>;

  // Desired result data
  desiredMethod: string;
  desiredPhotos: DesiredPhoto[];
  desiredAnalysisResult: DesiredColorAnalysisResult | null;
  desiredNotes: string;

  // Formulation data
  selectedBrand: string;
  selectedLine: string;
  formula: string;
  formulaId?: string; // ID for the formula (can be used for feedback tracking)
  formulationData?: Record<string, unknown>; // Structured formula data from AI
  applicationTechnique?: string;
  processingTime?: number;
  developerVolume?: number;
  isFormulaFromAI: boolean;
  formulaCost: FormulaCost | null;
  viabilityAnalysis: ViabilityAnalysis | null;
  smartViabilityAnalysis?: SmartViabilityAnalysis;
  stockValidation: {
    isChecking: boolean;
    hasStock: boolean;
    missingProducts: string[];
    checked: boolean;
  };

  // Result data
  resultImage: string | null;
  clientSatisfaction: number;
  resultNotes: string;

  // Feedback data (stored temporarily until service is saved)
  feedbackData?: {
    rating: number;
    worked_as_expected: boolean;
    would_use_again: boolean;
    actual_result: string;
    adjustments_made?: string;
    hair_type?: string;
    environmental_factors?: string;
  };
}

export const useServiceFlow = () => {
  const [currentStep, setCurrentStep] = useState(0);

  const [serviceData, setServiceData] = useState<ServiceData>({
    // Client info
    client: null,
    clientId: null,

    // Diagnosis data
    diagnosisMethod: 'ai',
    hairPhotos: [],
    hairThickness: '',
    hairDensity: '',
    overallTone: '',
    overallReflect: '',
    overallUndertone: '', // Mantener para compatibilidad
    lastChemicalProcessType: '',
    lastChemicalProcessDate: '',
    hasUsedHomeRemedies: false,
    hairLength: 0,
    monthlyGrowth: 1.25,
    diagnosisNotes: '',
    zoneColorAnalysis: {
      [HairZone.ROOTS]: { zone: HairZone.ROOTS },
      [HairZone.MIDS]: { zone: HairZone.MIDS },
      [HairZone.ENDS]: { zone: HairZone.ENDS },
    },
    zonePhysicalAnalysis: {
      [HairZone.ROOTS]: { zone: HairZone.ROOTS },
      [HairZone.MIDS]: { zone: HairZone.MIDS },
      [HairZone.ENDS]: { zone: HairZone.ENDS },
    },

    // Desired result data
    desiredMethod: 'ai',
    desiredPhotos: [],
    desiredAnalysisResult: null,
    desiredNotes: '',

    // Formulation data
    selectedBrand: 'Wella Professionals',
    selectedLine: 'Illumina Color',
    formula: '',
    isFormulaFromAI: true,
    formulaCost: null,
    viabilityAnalysis: null,
    stockValidation: {
      isChecking: false,
      hasStock: true,
      missingProducts: [],
      checked: false,
    },

    // Result data
    resultImage: null,
    clientSatisfaction: 5,
    resultNotes: '',
  });

  const updateServiceData = useCallback((updates: Partial<ServiceData>) => {
    setServiceData(prev => ({ ...prev, ...updates }));
  }, []);

  // Smart Transitions Integration
  const smartTransitions = useSmartTransitions({
    serviceData,
    currentStep,
    viabilityAnalysis: serviceData.smartViabilityAnalysis,
    onTransitionBlocked: (validation) => {
      Alert.alert(
        validation.title,
        validation.message,
        [{ text: 'Entendido', style: 'default' }]
      );
    },
    onTransitionWarning: (validation) => {
      // Las advertencias se manejan a través del diálogo
    },
  });

  const validateDiagnosis = useCallback(() => {
    const {
      hairThickness,
      hairDensity,
      overallTone,
      overallReflect,
      zoneColorAnalysis,
      zonePhysicalAnalysis,
    } = serviceData;

    // Check if all required fields are filled
    const hasGeneralData = hairThickness && hairDensity && overallTone && overallReflect;
    const hasAllZoneData =
      Object.values(zoneColorAnalysis).every(
        zone => zone.level && zone.tone && zone.reflect && zone.state
      ) &&
      Object.values(zonePhysicalAnalysis).every(
        zone => zone.porosity && zone.elasticity && zone.resistance && zone.damage
      );

    return hasGeneralData && hasAllZoneData;
  }, [serviceData]);

  const goToNextStep = useCallback(async () => {
    // If we're on diagnosis step, validate before continuing
    if (currentStep === 0 && !validateDiagnosis()) {
      Alert.alert(
        'Diagnóstico incompleto',
        'Por favor completa todos los campos requeridos antes de continuar.',
        [{ text: 'OK' }]
      );
      return;
    }

    const targetStep = currentStep + 1;
    if (targetStep < STEPS.length) {
      const canProceed = await smartTransitions.validateTransition(targetStep);
      if (canProceed) {
        setCurrentStep(targetStep);
      }
      // Si no puede proceder, el diálogo se mostrará automáticamente
    }
  }, [currentStep, validateDiagnosis, smartTransitions]);

  const goToPreviousStep = useCallback(() => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    } else {
      router.back();
    }
  }, [currentStep]);

  const goToStep = useCallback(async (stepIndex: number) => {
    if (stepIndex >= 0 && stepIndex < STEPS.length) {
      // Si vamos hacia atrás, permitir siempre
      if (stepIndex <= currentStep) {
        setCurrentStep(stepIndex);
        return;
      }

      // Si vamos hacia adelante, validar transición
      const canProceed = await smartTransitions.validateTransition(stepIndex);
      if (canProceed) {
        setCurrentStep(stepIndex);
      }
    }
  }, [currentStep, smartTransitions]);

  const canNavigateToStep = useCallback(
    (stepIndex: number) => {
      // Can always go back to previous steps
      if (stepIndex <= currentStep) return true;

      // Use smart transitions for forward navigation
      return smartTransitions.canProceedToStep(stepIndex);
    },
    [currentStep, smartTransitions]
  );

  return {
    currentStep,
    serviceData,
    updateServiceData,
    goToNextStep,
    goToPreviousStep,
    goToStep,
    canNavigateToStep,
    validateDiagnosis,
    STEPS,

    // Smart Transitions
    smartTransitions,
  };
};
