/**
 * Colorimetry Rules and Validation
 * Professional hair coloring principles and technical validations
 * EXPANDED: Comprehensive rule system with conversational explanations
 */

export interface ColorProcess {
  currentLevel: number;
  desiredLevel: number;
  currentState: 'natural' | 'colored' | 'bleached' | 'mixed';
  hasMetallicSalts?: boolean;
  hasHenna?: boolean;
}

export interface ProcessValidation {
  isViable: boolean;
  requiredProcesses: ProcessType[];
  warnings: string[];
  recommendedDeveloperVolume: number;
  estimatedSessions: number;
}

export enum ProcessType {
  DIRECT_COLOR = 'direct_color',
  BLEACHING = 'bleaching',
  COLOR_REMOVAL = 'color_removal',
  PRE_PIGMENTATION = 'pre_pigmentation',
  NEUTRALIZATION = 'neutralization',
  TONING = 'toning',
}

// NEW: Comprehensive Rule System
export interface ColorimetryRule {
  id: string;
  name: string;
  description: string;
  conversationalExplanation: string;
  severity: 'critical' | 'warning' | 'info';
  category: 'safety' | 'chemistry' | 'technique' | 'timing';
  validator: (process: ColorProcess) => boolean;
  violationMessage: string;
  conversationalViolation: string;
  suggestedAction: string;
  alternatives: string[];
}

export interface UnderlyingPigment {
  level: number;
  dominantPigment: string;
  neutralizer: string;
  description: string;
}

/**
 * Core colorimetry principles
 */
export const COLORIMETRY_PRINCIPLES = {
  // Color cannot lift color
  COLOR_LIFT_LIMIT: {
    natural: 3, // Natural hair can be lifted up to 3 levels with color
    colored: 0, // Colored hair cannot be lifted with color
    bleached: 0, // Bleached hair cannot be lifted with color
  },

  // Developer volume guidelines
  DEVELOPER_VOLUMES: {
    depositing: 10, // For depositing color only (going darker)
    gray_coverage: 20, // Standard gray coverage
    lifting_1_2: 20, // Lifting 1-2 levels
    lifting_2_3: 30, // Lifting 2-3 levels
    lifting_3_4: 40, // Lifting 3-4 levels (max in some regions)
    bleaching: 30, // Standard for bleaching
  },

  // Pre-pigmentation thresholds
  PRE_PIGMENT_THRESHOLD: 3, // Need pre-pigment when going darker by 3+ levels

  // Maximum lift per session
  MAX_LIFT_PER_SESSION: 4,
};

/**
 * THE SACRED RULES OF COLORIMETRY
 * These are the fundamental, non-negotiable principles that ensure safe and predictable results
 */
export const SACRED_COLORIMETRY_RULES: ColorimetryRule[] = [
  {
    id: 'tinte_no_aclara_tinte',
    name: 'Tinte No Aclara Tinte',
    description: 'El color artificial no puede ser aclarado con otro tinte',
    conversationalExplanation: 'Esta es la regla más importante de la colorimetría. El pigmento artificial depositado en el cabello no puede ser removido con tinte, solo con productos específicos de extracción.',
    severity: 'critical',
    category: 'chemistry',
    validator: (process) => !(process.currentState === 'colored' && process.desiredLevel > process.currentLevel),
    violationMessage: 'Intento de aclarar cabello teñido con tinte',
    conversationalViolation: '¡Atención! Estás intentando aclarar cabello previamente teñido. Esto no es posible con tinte y puede resultar en un color impredecible o dañar el cabello.',
    suggestedAction: 'Realizar extracción de color artificial antes de la coloración',
    alternatives: [
      'Proceso de extracción + coloración en 2 sesiones',
      'Cambiar a un tono más oscuro o similar',
      'Usar técnicas de mechas para crear contraste sin tocar la base'
    ]
  },
  {
    id: 'underlying_pigment_neutralization',
    name: 'Neutralización del Fondo de Aclaración',
    description: 'Todo proceso de aclaración revela pigmentos subyacentes que deben ser neutralizados',
    conversationalExplanation: 'Cuando aclaramos el cabello, aparecen pigmentos naturales (naranjas, amarillos) que deben ser neutralizados para obtener el tono deseado.',
    severity: 'warning',
    category: 'technique',
    validator: (process) => {
      const levelDiff = process.desiredLevel - process.currentLevel;
      return levelDiff <= 2; // Si aclaramos más de 2 niveles, necesitamos neutralización
    },
    violationMessage: 'Aclaración significativa sin considerar neutralización',
    conversationalViolation: 'Al aclarar más de 2 niveles, aparecerán reflejos naranjas o amarillos que necesitan ser neutralizados para lograr el tono deseado.',
    suggestedAction: 'Incluir matices neutralizadores en la fórmula',
    alternatives: [
      'Usar cenizas para neutralizar naranjas',
      'Usar violetas para neutralizar amarillos',
      'Proceso de pre-decoloración + matización'
    ]
  },
  {
    id: 'developer_volume_safety',
    name: 'Volumen de Oxidante Seguro',
    description: 'El volumen de oxidante debe ser apropiado para el nivel de aclaración deseado',
    conversationalExplanation: 'Usar más oxidante del necesario daña innecesariamente el cabello. Usar menos del necesario no logra el resultado deseado.',
    severity: 'warning',
    category: 'safety',
    validator: (process) => {
      const levelDiff = process.desiredLevel - process.currentLevel;
      return levelDiff <= 3; // Más de 3 niveles requiere consideración especial
    },
    violationMessage: 'Volumen de oxidante inadecuado para el cambio deseado',
    conversationalViolation: 'El cambio que quieres hacer requiere un volumen de oxidante específico. Usar el volumen incorrecto puede dañar el cabello o no lograr el resultado.',
    suggestedAction: 'Ajustar volumen de oxidante según la tabla de aclaración',
    alternatives: [
      'Vol. 20 para 1-2 niveles de aclaración',
      'Vol. 30 para 2-3 niveles de aclaración',
      'Vol. 40 para 3-4 niveles (solo si está permitido regionalmente)'
    ]
  },
  {
    id: 'gray_coverage_formula',
    name: 'Cobertura de Canas',
    description: 'Las canas requieren fórmulas específicas para cobertura completa',
    conversationalExplanation: 'Las canas son resistentes al color porque no tienen melanina. Necesitan fórmulas con más pigmento y técnicas específicas.',
    severity: 'warning',
    category: 'technique',
    validator: (process) => true, // Esta regla se evalúa por porcentaje de canas
    violationMessage: 'Fórmula inadecuada para porcentaje de canas',
    conversationalViolation: 'Con este porcentaje de canas, necesitas ajustar la fórmula para garantizar cobertura completa y durabilidad.',
    suggestedAction: 'Incluir base natural en la mezcla para canas',
    alternatives: [
      'Mezclar 50% tono deseado + 50% base natural',
      'Usar línea específica para canas',
      'Pre-pigmentación en casos de canas rebeldes'
    ]
  },
  {
    id: 'metallic_salts_incompatibility',
    name: 'Incompatibilidad con Sales Metálicas',
    description: 'Las sales metálicas pueden causar reacciones químicas peligrosas',
    conversationalExplanation: 'Algunos tintes caseros y productos de farmacia contienen sales metálicas que reaccionan violentamente con productos profesionales.',
    severity: 'critical',
    category: 'safety',
    validator: (process) => !process.hasMetallicSalts,
    violationMessage: 'Presencia de sales metálicas detectada',
    conversationalViolation: '¡PELIGRO! He detectado posibles sales metálicas. Aplicar productos profesionales puede causar humo, calor excesivo o rotura del cabello.',
    suggestedAction: 'Test de mechón obligatorio y tratamiento de eliminación',
    alternatives: [
      'Test de mechón con producto específico',
      'Tratamiento de eliminación de sales metálicas',
      'Esperar 2-4 semanas antes de cualquier proceso químico'
    ]
  },
  {
    id: 'henna_compatibility',
    name: 'Compatibilidad con Henna',
    description: 'La henna natural puede reaccionar impredeciblemente con productos químicos',
    conversationalExplanation: 'La henna forma una capa protectora en el cabello que puede interferir con la penetración del color y causar resultados inesperados.',
    severity: 'critical',
    category: 'chemistry',
    validator: (process) => !process.hasHenna,
    violationMessage: 'Cabello con henna detectado',
    conversationalViolation: 'La henna puede causar resultados impredecibles. El color puede no penetrar uniformemente o crear tonos no deseados.',
    suggestedAction: 'Test de mechón extensivo y productos específicos',
    alternatives: [
      'Test de mechón en zona oculta',
      'Usar solo productos compatibles con henna',
      'Esperar que la henna se desvanezca naturalmente (6-12 meses)'
    ]
  }
];

/**
 * UNDERLYING PIGMENT CHART
 * Shows what pigments are revealed at each level of lightening
 */
export const UNDERLYING_PIGMENT_CHART: UnderlyingPigment[] = [
  { level: 1, dominantPigment: 'Negro', neutralizer: 'N/A', description: 'Negro natural - no se aclara' },
  { level: 2, dominantPigment: 'Castaño muy oscuro', neutralizer: 'N/A', description: 'Castaño muy oscuro - aclaración mínima' },
  { level: 3, dominantPigment: 'Castaño oscuro', neutralizer: 'Ceniza', description: 'Castaño oscuro con reflejos rojizos' },
  { level: 4, dominantPigment: 'Rojo-naranja', neutralizer: 'Ceniza/Verde', description: 'Reflejos rojos y naranjas prominentes' },
  { level: 5, dominantPigment: 'Naranja', neutralizer: 'Ceniza', description: 'Naranja intenso - requiere neutralización' },
  { level: 6, dominantPigment: 'Naranja-amarillo', neutralizer: 'Ceniza/Violeta', description: 'Transición naranja a amarillo' },
  { level: 7, dominantPigment: 'Amarillo-naranja', neutralizer: 'Violeta/Ceniza', description: 'Amarillo con trazas naranjas' },
  { level: 8, dominantPigment: 'Amarillo', neutralizer: 'Violeta', description: 'Amarillo puro - base para rubios' },
  { level: 9, dominantPigment: 'Amarillo pálido', neutralizer: 'Violeta suave', description: 'Amarillo muy claro' },
  { level: 10, dominantPigment: 'Amarillo muy pálido', neutralizer: 'Matiz violeta', description: 'Base perfecta para platinos' }
];

/**
 * Validates if a color process is technically viable and determines required steps
 */
export function validateColorProcess(
  process: ColorProcess,
  regionalMaxVolume: number = 40
): ProcessValidation {
  const { currentLevel, desiredLevel, currentState, hasMetallicSalts, hasHenna } = process;
  const levelDifference = desiredLevel - currentLevel;

  const validation: ProcessValidation = {
    isViable: true,
    requiredProcesses: [],
    warnings: [],
    recommendedDeveloperVolume: 20,
    estimatedSessions: 1,
  };

  // Check for incompatible products
  if (hasMetallicSalts) {
    validation.warnings.push(
      'Presencia de sales metálicas detectada. Requiere test de mechón y posible tratamiento de eliminación.'
    );
    validation.isViable = false;
    return validation;
  }

  if (hasHenna) {
    validation.warnings.push(
      'Presencia de henna detectada. La decoloración puede causar reacciones impredecibles.'
    );
    validation.warnings.push('Se recomienda cortar el cabello con henna o esperar a que crezca.');
  }

  // Determine process based on level change and current state
  if (levelDifference > 0) {
    // LIGHTENING PROCESS
    const maxLiftWithColor = COLORIMETRY_PRINCIPLES.COLOR_LIFT_LIMIT[currentState];

    if (levelDifference > maxLiftWithColor) {
      // Cannot achieve with color alone
      if (currentState === 'colored') {
        validation.requiredProcesses.push(ProcessType.COLOR_REMOVAL);
        validation.warnings.push(
          'Color no levanta color. Se requiere decapado previo para eliminar pigmentos artificiales.'
        );
      }

      validation.requiredProcesses.push(ProcessType.BLEACHING);

      // Calculate sessions needed
      const liftsNeeded = levelDifference - maxLiftWithColor;
      validation.estimatedSessions = Math.ceil(
        liftsNeeded / COLORIMETRY_PRINCIPLES.MAX_LIFT_PER_SESSION
      );

      if (validation.estimatedSessions > 1) {
        validation.warnings.push(
          `Se requieren ${validation.estimatedSessions} sesiones para alcanzar el nivel deseado de forma segura.`
        );
      }

      // Set developer volume for bleaching
      validation.recommendedDeveloperVolume = Math.min(30, regionalMaxVolume);
    } else {
      // Can achieve with color
      validation.requiredProcesses.push(ProcessType.DIRECT_COLOR);

      // Determine developer volume based on lift needed
      if (levelDifference <= 1) {
        validation.recommendedDeveloperVolume = 20;
      } else if (levelDifference <= 2) {
        validation.recommendedDeveloperVolume = Math.min(30, regionalMaxVolume);
      } else {
        validation.recommendedDeveloperVolume = Math.min(40, regionalMaxVolume);
      }
    }
  } else if (levelDifference < 0) {
    // DARKENING PROCESS
    const levelsDarker = Math.abs(levelDifference);

    if (levelsDarker >= COLORIMETRY_PRINCIPLES.PRE_PIGMENT_THRESHOLD) {
      validation.requiredProcesses.push(ProcessType.PRE_PIGMENTATION);
      validation.warnings.push(
        `Pre-pigmentación requerida al oscurecer ${levelsDarker} niveles para evitar resultados verdosos.`
      );
    }

    validation.requiredProcesses.push(ProcessType.DIRECT_COLOR);

    // For darkening, only need low volume developer
    validation.recommendedDeveloperVolume = COLORIMETRY_PRINCIPLES.DEVELOPER_VOLUMES.depositing;
  } else {
    // SAME LEVEL (tone change)
    validation.requiredProcesses.push(ProcessType.DIRECT_COLOR);

    // Check if it's just a toning process
    if (currentState === 'bleached' || currentLevel >= 9) {
      validation.requiredProcesses = [ProcessType.TONING];
      validation.recommendedDeveloperVolume = COLORIMETRY_PRINCIPLES.DEVELOPER_VOLUMES.depositing;
    } else {
      validation.recommendedDeveloperVolume =
        COLORIMETRY_PRINCIPLES.DEVELOPER_VOLUMES.gray_coverage;
    }
  }

  // Add specific warnings for challenging processes
  if (currentLevel <= 3 && desiredLevel >= 9) {
    validation.warnings.push(
      'Proceso de alto riesgo: aclarar cabello muy oscuro a rubio muy claro puede causar daño severo.'
    );
    validation.warnings.push(
      'Se recomienda encarecidamente realizar el proceso en múltiples sesiones con tratamientos intermedios.'
    );
  }

  if (currentState === 'bleached' && levelDifference < -3) {
    validation.warnings.push(
      'Oscurecer cabello decolorado requiere especial atención a la porosidad.'
    );
    validation.warnings.push(
      'Considerar usar un relleno de color o tratamiento de porosidad previo.'
    );
  }

  return validation;
}

/**
 * Determines the specific colorimetry instructions for the AI
 */
export function getColorimetryInstructions(
  validation: ProcessValidation,
  lang: 'es' | 'en' = 'es'
): string {
  const instructions: string[] = [];

  if (lang === 'es') {
    // Spanish instructions
    instructions.push('**PRINCIPIOS DE COLORIMETRÍA APLICADOS:**');

    validation.requiredProcesses.forEach(process => {
      switch (process) {
        case ProcessType.COLOR_REMOVAL:
          instructions.push(
            '- DECAPADO REQUERIDO: Eliminar pigmentos artificiales antes de aclarar'
          );
          instructions.push('  - Usar producto decapante suave si es posible');
          instructions.push('  - Evaluar el estado del cabello después del decapado');
          break;

        case ProcessType.BLEACHING:
          instructions.push('- DECOLORACIÓN REQUERIDA: Para alcanzar el nivel deseado');
          instructions.push(
            `  - Usar oxidante de ${validation.recommendedDeveloperVolume} volúmenes máximo`
          );
          instructions.push('  - Monitorear constantemente el proceso');
          break;

        case ProcessType.PRE_PIGMENTATION:
          instructions.push('- PRE-PIGMENTACIÓN REQUERIDA: Para evitar tonos no deseados');
          instructions.push('  - Aplicar pigmento cálido del nivel intermedio');
          instructions.push('  - Sin oxidante o con 10 volúmenes máximo');
          break;

        case ProcessType.DIRECT_COLOR:
          instructions.push(
            `- COLORACIÓN DIRECTA: Oxidante de ${validation.recommendedDeveloperVolume} volúmenes`
          );
          break;

        case ProcessType.TONING:
          instructions.push('- MATIZACIÓN: Proceso de depósito únicamente');
          instructions.push('  - Usar oxidante de 10 volúmenes o activador sin amoniaco');
          break;
      }
    });

    instructions.push('\n**VOLUMEN DE OXIDANTE:**');
    instructions.push(`- USA EXACTAMENTE ${validation.recommendedDeveloperVolume} VOLÚMENES`);
    instructions.push('- NO uses volumen mayor al necesario');
  } else {
    // English instructions
    instructions.push('**APPLIED COLORIMETRY PRINCIPLES:**');

    validation.requiredProcesses.forEach(process => {
      switch (process) {
        case ProcessType.COLOR_REMOVAL:
          instructions.push(
            '- COLOR REMOVAL REQUIRED: Remove artificial pigments before lightening'
          );
          instructions.push('  - Use gentle color remover if possible');
          instructions.push('  - Evaluate hair condition after removal');
          break;

        case ProcessType.BLEACHING:
          instructions.push('- BLEACHING REQUIRED: To achieve desired level');
          instructions.push(
            `  - Use maximum ${validation.recommendedDeveloperVolume} volume developer`
          );
          instructions.push('  - Monitor process constantly');
          break;

        case ProcessType.PRE_PIGMENTATION:
          instructions.push('- PRE-PIGMENTATION REQUIRED: To avoid unwanted tones');
          instructions.push('  - Apply warm pigment at intermediate level');
          instructions.push('  - No developer or maximum 10 volume');
          break;

        case ProcessType.DIRECT_COLOR:
          instructions.push(
            `- DIRECT COLORING: ${validation.recommendedDeveloperVolume} volume developer`
          );
          break;

        case ProcessType.TONING:
          instructions.push('- TONING: Deposit only process');
          instructions.push('  - Use 10 volume developer or ammonia-free activator');
          break;
      }
    });

    instructions.push('\n**DEVELOPER VOLUME:**');
    instructions.push(`- USE EXACTLY ${validation.recommendedDeveloperVolume} VOLUMES`);
    instructions.push('- DO NOT use higher volume than necessary');
  }

  return instructions.join('\n');
}

/**
 * NEW: Enhanced Rule Validation Functions
 */

/**
 * Validate all sacred rules against a color process
 */
export function validateSacredRules(process: ColorProcess, grayPercentage: number = 0): ColorimetryRule[] {
  const violations: ColorimetryRule[] = [];

  for (const rule of SACRED_COLORIMETRY_RULES) {
    // Special handling for gray coverage rule
    if (rule.id === 'gray_coverage_formula') {
      if (grayPercentage > 50) {
        violations.push(rule);
      }
      continue;
    }

    // Standard rule validation
    if (!rule.validator(process)) {
      violations.push(rule);
    }
  }

  return violations;
}

/**
 * Get underlying pigment information for a specific level
 */
export function getUnderlyingPigment(level: number): UnderlyingPigment | null {
  return UNDERLYING_PIGMENT_CHART.find(pigment => pigment.level === level) || null;
}

/**
 * Get neutralizer recommendation for a level change
 */
export function getNeutralizationRecommendation(currentLevel: number, desiredLevel: number): string {
  const levelDifference = desiredLevel - currentLevel;

  if (levelDifference <= 0) {
    return 'No se requiere neutralización para procesos de oscurecimiento';
  }

  if (levelDifference <= 2) {
    return 'Neutralización mínima - considera matices ceniza suaves';
  }

  const targetLevel = Math.min(currentLevel + levelDifference, 10);
  const underlyingPigment = getUnderlyingPigment(targetLevel);

  if (!underlyingPigment) {
    return 'Consulta tabla de neutralización para este nivel';
  }

  return `Neutralización requerida: ${underlyingPigment.neutralizer} para contrarrestar ${underlyingPigment.dominantPigment.toLowerCase()}`;
}

/**
 * Get conversational explanation for a rule violation
 */
export function getConversationalExplanation(ruleId: string): string {
  const rule = SACRED_COLORIMETRY_RULES.find(r => r.id === ruleId);
  return rule?.conversationalExplanation || 'Explicación no disponible';
}

/**
 * Get suggested alternatives for a rule violation
 */
export function getSuggestedAlternatives(ruleId: string): string[] {
  const rule = SACRED_COLORIMETRY_RULES.find(r => r.id === ruleId);
  return rule?.alternatives || [];
}

/**
 * Check if a process requires multiple sessions
 */
export function requiresMultipleSessions(process: ColorProcess): { required: boolean; reason: string; sessions: number } {
  const levelDifference = Math.abs(process.desiredLevel - process.currentLevel);

  // Critical violations always require multiple sessions
  const violations = validateSacredRules(process);
  const criticalViolations = violations.filter(v => v.severity === 'critical');

  if (criticalViolations.length > 0) {
    return {
      required: true,
      reason: 'Violaciones críticas detectadas que requieren tratamiento previo',
      sessions: 2
    };
  }

  // Large level changes require multiple sessions
  if (levelDifference > 4) {
    return {
      required: true,
      reason: 'Cambio de nivel demasiado extremo para una sola sesión',
      sessions: Math.ceil(levelDifference / 3)
    };
  }

  // Colored hair being lightened requires multiple sessions
  if (process.currentState === 'colored' && process.desiredLevel > process.currentLevel) {
    return {
      required: true,
      reason: 'Extracción de color artificial requerida antes de aclaración',
      sessions: 2
    };
  }

  return {
    required: false,
    reason: 'Proceso viable en una sola sesión',
    sessions: 1
  };
}

/**
 * Generate a comprehensive safety report
 */
export function generateSafetyReport(process: ColorProcess, grayPercentage: number = 0): {
  overallSafety: 'safe' | 'caution' | 'dangerous';
  violations: ColorimetryRule[];
  recommendations: string[];
  sessionPlan: { required: boolean; reason: string; sessions: number };
} {
  const violations = validateSacredRules(process, grayPercentage);
  const sessionPlan = requiresMultipleSessions(process);

  const criticalCount = violations.filter(v => v.severity === 'critical').length;
  const warningCount = violations.filter(v => v.severity === 'warning').length;

  let overallSafety: 'safe' | 'caution' | 'dangerous';
  if (criticalCount > 0) {
    overallSafety = 'dangerous';
  } else if (warningCount > 0 || sessionPlan.required) {
    overallSafety = 'caution';
  } else {
    overallSafety = 'safe';
  }

  const recommendations: string[] = [];

  // Add specific recommendations based on violations
  violations.forEach(violation => {
    recommendations.push(violation.suggestedAction);
  });

  // Add neutralization recommendations
  if (process.desiredLevel > process.currentLevel) {
    recommendations.push(getNeutralizationRecommendation(process.currentLevel, process.desiredLevel));
  }

  return {
    overallSafety,
    violations,
    recommendations,
    sessionPlan
  };
}
