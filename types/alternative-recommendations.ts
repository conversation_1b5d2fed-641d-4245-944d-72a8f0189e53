/**
 * Tipos compartidos para el sistema de recomendaciones alternativas
 */

import { HairZone, ZoneColorAnalysis } from './hair-diagnosis';

export enum ViabilityStatus {
  GREEN = 'GREEN',    // 1 sesión - Proceso directo y seguro
  AMBER = 'AMBER',    // Múltiples sesiones - Proceso gradual recomendado
  RED = 'RED'         // No recomendado - Requiere tratamiento previo o alternativas
}

export interface AlternativeRecommendation {
  targetColor: string;
  reason: string;
  viabilityImprovement: ViabilityStatus;
  description: string;
  benefits?: string[];
  considerations?: string[];
}

export interface AlternativeGenerationContext {
  currentLevel: number;
  currentTone: string;
  desiredLevel: number;
  desiredTone: string;
  hairCondition: 'excellent' | 'good' | 'fair' | 'poor' | 'damaged';
  technique: string;
  riskFactors: string[];
  zoneAnalysis: Record<HairZone, Partial<ZoneColorAnalysis>>;
}

export interface AlternativeCategory {
  id: string;
  name: string;
  description: string;
  priority: number;
  alternatives: AlternativeRecommendation[];
}
