/**
 * Tipos compartidos para el sistema de recomendaciones alternativas
 */

import { HairZone, ZoneColorAnalysis } from './hair-diagnosis';

// Estados del semáforo de viabilidad
export const ViabilityStatus = {
  GREEN: 'GREEN' as const,    // 1 sesión - Proceso directo y seguro
  AMBER: 'AMBER' as const,    // Múltiples sesiones - Proceso gradual recomendado
  RED: 'RED' as const         // No recomendado - Requiere tratamiento previo o alternativas
} as const;

export type ViabilityStatus = typeof ViabilityStatus[keyof typeof ViabilityStatus];

export interface AlternativeRecommendation {
  targetColor: string;
  reason: string;
  viabilityImprovement: ViabilityStatus;
  description: string;
  benefits?: string[];
  considerations?: string[];
}

export interface AlternativeGenerationContext {
  currentLevel: number;
  currentTone: string;
  desiredLevel: number;
  desiredTone: string;
  hairCondition: 'excellent' | 'good' | 'fair' | 'poor' | 'damaged';
  technique: string;
  riskFactors: string[];
  zoneAnalysis: Record<HairZone, Partial<ZoneColorAnalysis>>;
}

export interface AlternativeCategory {
  id: string;
  name: string;
  description: string;
  priority: number;
  alternatives: AlternativeRecommendation[];
}
