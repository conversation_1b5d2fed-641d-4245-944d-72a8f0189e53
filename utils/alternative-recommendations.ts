/**
 * Sistema de Recomendaciones Alternativas
 * Genera automáticamente alternativas viables cuando el color deseado es problemático
 */

import { DesiredColorAnalysisResult } from '@/types/desired-analysis';
import { HairZone, ZoneColorAnalysis } from '@/types/hair-diagnosis';
import { AlternativeRecommendation, ViabilityStatus } from './viability-analyzer';

export interface AlternativeGenerationContext {
  currentLevel: number;
  currentTone: string;
  desiredLevel: number;
  desiredTone: string;
  hairCondition: 'excellent' | 'good' | 'fair' | 'poor' | 'damaged';
  technique: string;
  riskFactors: string[];
  zoneAnalysis: Record<HairZone, Partial<ZoneColorAnalysis>>;
}

export interface AlternativeCategory {
  id: string;
  name: string;
  description: string;
  priority: number;
  alternatives: AlternativeRecommendation[];
}

/**
 * Genera alternativas automáticas basadas en el contexto del cabello
 */
export function generateAlternativeRecommendations(
  context: AlternativeGenerationContext
): AlternativeCategory[] {
  const categories: AlternativeCategory[] = [];

  // 1. Alternativas de Aclaración Gradual
  if (context.desiredLevel > context.currentLevel + 3) {
    categories.push(generateGradualLighteningAlternatives(context));
  }

  // 2. Alternativas de Técnica
  if (context.technique === 'full_color' && context.hairCondition !== 'excellent') {
    categories.push(generateTechniqueAlternatives(context));
  }

  // 3. Alternativas de Tono
  if (isProblematicToneChange(context)) {
    categories.push(generateToneAlternatives(context));
  }

  // 4. Alternativas de Mantenimiento
  if (context.desiredLevel > 8) {
    categories.push(generateMaintenanceAlternatives(context));
  }

  // 5. Alternativas de Tratamiento Previo
  if (context.hairCondition === 'poor' || context.hairCondition === 'damaged') {
    categories.push(generateTreatmentAlternatives(context));
  }

  return categories.sort((a, b) => b.priority - a.priority);
}

/**
 * Alternativas de aclaración gradual
 */
function generateGradualLighteningAlternatives(
  context: AlternativeGenerationContext
): AlternativeCategory {
  const levelDifference = context.desiredLevel - context.currentLevel;
  const intermediateLevel = context.currentLevel + Math.floor(levelDifference / 2);
  
  const alternatives: AlternativeRecommendation[] = [];

  // Primera sesión a nivel intermedio
  alternatives.push({
    targetColor: `Nivel ${intermediateLevel}`,
    reason: 'Aclaración gradual más segura',
    viabilityImprovement: ViabilityStatus.AMBER,
    description: `Primer paso hacia el objetivo final. Aclara ${intermediateLevel - context.currentLevel} niveles de forma segura, preparando el cabello para la siguiente sesión.`,
    benefits: [
      'Menor daño capilar',
      'Mejor retención del color',
      'Proceso más controlado'
    ],
    considerations: [
      'Requiere múltiples sesiones',
      'Mayor inversión de tiempo',
      'Resultado gradual'
    ]
  });

  // Mechas como alternativa
  if (levelDifference > 4) {
    alternatives.push({
      targetColor: 'Mechas Balayage',
      reason: 'Efecto luminoso sin comprometer todo el cabello',
      viabilityImprovement: ViabilityStatus.GREEN,
      description: 'Crea luminosidad y dimensión aclarando solo secciones estratégicas del cabello, manteniendo la base natural.',
      benefits: [
        'Daño mínimo',
        'Efecto natural',
        'Fácil mantenimiento'
      ],
      considerations: [
        'Resultado diferente al objetivo',
        'Crecimiento menos notorio',
        'Técnica específica requerida'
      ]
    });
  }

  return {
    id: 'gradual_lightening',
    name: 'Aclaración Gradual',
    description: 'Opciones para lograr el nivel deseado de forma segura en múltiples sesiones',
    priority: 9,
    alternatives
  };
}

/**
 * Alternativas de técnica
 */
function generateTechniqueAlternatives(
  context: AlternativeGenerationContext
): AlternativeCategory {
  const alternatives: AlternativeRecommendation[] = [];

  // Balayage como alternativa suave
  alternatives.push({
    targetColor: 'Balayage Suave',
    reason: 'Técnica menos agresiva para cabello comprometido',
    viabilityImprovement: ViabilityStatus.GREEN,
    description: 'Aplicación selectiva que respeta las zonas más dañadas mientras crea el efecto deseado.',
    benefits: [
      'Respeta el cabello dañado',
      'Resultado natural',
      'Menor tiempo de procesamiento'
    ]
  });

  // Ombré como opción gradual
  if (context.currentLevel < context.desiredLevel) {
    alternatives.push({
      targetColor: 'Ombré Degradado',
      reason: 'Concentra la aclaración en medios y puntas',
      viabilityImprovement: ViabilityStatus.AMBER,
      description: 'Mantiene la raíz natural y aclara gradualmente hacia las puntas, reduciendo el impacto en el cabello.',
      benefits: [
        'Raíz natural protegida',
        'Efecto moderno',
        'Crecimiento disimulado'
      ]
    });
  }

  return {
    id: 'technique_alternatives',
    name: 'Técnicas Alternativas',
    description: 'Métodos de aplicación más suaves para cabello comprometido',
    priority: 7,
    alternatives
  };
}

/**
 * Alternativas de tono
 */
function generateToneAlternatives(
  context: AlternativeGenerationContext
): AlternativeCategory {
  const alternatives: AlternativeRecommendation[] = [];

  // Tono más cálido si busca frío en cabello naranja
  if (context.desiredTone.includes('ceniza') && context.currentTone.includes('dorado')) {
    alternatives.push({
      targetColor: `Nivel ${context.desiredLevel} Dorado`,
      reason: 'Trabaja con el tono natural del cabello',
      viabilityImprovement: ViabilityStatus.GREEN,
      description: 'Aprovecha los pigmentos naturales dorados para un resultado más armonioso y duradero.',
      benefits: [
        'Mayor durabilidad',
        'Proceso más suave',
        'Resultado más natural'
      ]
    });

    alternatives.push({
      targetColor: `Nivel ${context.desiredLevel} Beige`,
      reason: 'Tono intermedio entre cálido y frío',
      viabilityImprovement: ViabilityStatus.AMBER,
      description: 'Neutraliza parcialmente los tonos dorados sin llegar a ceniza completa.',
      benefits: [
        'Compromiso equilibrado',
        'Menos neutralización requerida',
        'Transición gradual posible'
      ]
    });
  }

  // Tono más oscuro si el deseado es muy claro
  if (context.desiredLevel > context.currentLevel + 4) {
    alternatives.push({
      targetColor: `Nivel ${context.currentLevel + 2} ${context.desiredTone}`,
      reason: 'Aclaración moderada con el tono deseado',
      viabilityImprovement: ViabilityStatus.GREEN,
      description: 'Logra el tono deseado en un nivel más seguro, manteniendo la integridad del cabello.',
      benefits: [
        'Tono objetivo logrado',
        'Menor daño',
        'Base para futuras aclaraciones'
      ]
    });
  }

  return {
    id: 'tone_alternatives',
    name: 'Alternativas de Tono',
    description: 'Opciones de color que trabajan mejor con el cabello actual',
    priority: 8,
    alternatives
  };
}

/**
 * Alternativas de mantenimiento
 */
function generateMaintenanceAlternatives(
  context: AlternativeGenerationContext
): AlternativeCategory {
  const alternatives: AlternativeRecommendation[] = [];

  // Mechas para mantenimiento más fácil
  alternatives.push({
    targetColor: 'Mechas Estratégicas',
    reason: 'Mantenimiento más espaciado y económico',
    viabilityImprovement: ViabilityStatus.GREEN,
    description: 'Crea luminosidad con mechas que requieren retoque cada 3-4 meses en lugar de cada 6-8 semanas.',
    benefits: [
      'Menor frecuencia de retoque',
      'Más económico a largo plazo',
      'Crecimiento natural disimulado'
    ]
  });

  // Color de raíz más oscuro
  alternatives.push({
    targetColor: `Raíz Nivel ${context.currentLevel + 1}, Medios/Puntas Nivel ${context.desiredLevel}`,
    reason: 'Efecto degradado con mantenimiento reducido',
    viabilityImprovement: ViabilityStatus.AMBER,
    description: 'Mantiene la raíz más oscura para disimular el crecimiento natural.',
    benefits: [
      'Crecimiento menos notorio',
      'Efecto dimensional',
      'Mantenimiento espaciado'
    ]
  });

  return {
    id: 'maintenance_alternatives',
    name: 'Alternativas de Mantenimiento',
    description: 'Opciones que facilitan el mantenimiento a largo plazo',
    priority: 6,
    alternatives
  };
}

/**
 * Alternativas de tratamiento previo
 */
function generateTreatmentAlternatives(
  context: AlternativeGenerationContext
): AlternativeCategory {
  const alternatives: AlternativeRecommendation[] = [];

  // Tratamiento + color suave
  alternatives.push({
    targetColor: 'Tratamiento Reconstructivo + Color Suave',
    reason: 'Prioriza la salud capilar antes del cambio',
    viabilityImprovement: ViabilityStatus.AMBER,
    description: 'Sesión de tratamiento intensivo seguida de coloración suave para preparar el cabello.',
    benefits: [
      'Cabello más fuerte',
      'Mejor retención del color',
      'Resultado más duradero'
    ]
  });

  // Gloss como alternativa temporal
  alternatives.push({
    targetColor: 'Gloss Temporal',
    reason: 'Prueba el tono sin compromiso permanente',
    viabilityImprovement: ViabilityStatus.GREEN,
    description: 'Tratamiento temporal que aporta brillo y un toque del tono deseado sin daño.',
    benefits: [
      'Sin daño capilar',
      'Resultado temporal',
      'Prueba antes del cambio definitivo'
    ]
  });

  return {
    id: 'treatment_alternatives',
    name: 'Alternativas con Tratamiento',
    description: 'Opciones que priorizan la salud del cabello',
    priority: 10,
    alternatives
  };
}

/**
 * Determina si el cambio de tono es problemático
 */
function isProblematicToneChange(context: AlternativeGenerationContext): boolean {
  // Cambio de cálido a frío en cabello muy dorado
  if (context.currentTone.includes('dorado') && context.desiredTone.includes('ceniza')) {
    return true;
  }

  // Cambio drástico de tono
  const problematicCombinations = [
    { from: 'rojizo', to: 'ceniza' },
    { from: 'naranja', to: 'ceniza' },
    { from: 'dorado', to: 'platino' }
  ];

  return problematicCombinations.some(combo => 
    context.currentTone.includes(combo.from) && context.desiredTone.includes(combo.to)
  );
}

/**
 * Filtra alternativas por viabilidad mínima
 */
export function filterAlternativesByViability(
  categories: AlternativeCategory[],
  minViability: ViabilityStatus = ViabilityStatus.AMBER
): AlternativeCategory[] {
  return categories.map(category => ({
    ...category,
    alternatives: category.alternatives.filter(alt => {
      if (minViability === ViabilityStatus.GREEN) {
        return alt.viabilityImprovement === ViabilityStatus.GREEN;
      }
      if (minViability === ViabilityStatus.AMBER) {
        return alt.viabilityImprovement !== ViabilityStatus.RED;
      }
      return true;
    })
  })).filter(category => category.alternatives.length > 0);
}

/**
 * Obtiene la mejor alternativa de cada categoría
 */
export function getBestAlternatives(categories: AlternativeCategory[]): AlternativeRecommendation[] {
  return categories
    .sort((a, b) => b.priority - a.priority)
    .map(category => category.alternatives[0])
    .filter(Boolean);
}
