/**
 * Sistema de Transiciones Inteligentes Entre Pasos
 * Evalúa la complejidad del caso y muestra advertencias contextuales
 */

import { ServiceData } from '@/src/service/hooks/useServiceFlow';
import { analyzeSmartViability, ViabilityStatus, SmartViabilityAnalysis } from './viability-analyzer';
import { HairZone } from '@/types/hair-diagnosis';

export enum TransitionType {
  DIAGNOSIS_TO_DESIRED = 'diagnosis_to_desired',
  DESIRED_TO_FORMULATION = 'desired_to_formulation',
  FORMULATION_TO_COMPLETION = 'formulation_to_completion'
}

export enum TransitionStatus {
  PROCEED = 'PROCEED',           // Continuar sin advertencias
  WARNING = 'WARNING',           // Mostrar advertencia pero permitir continuar
  CONFIRMATION = 'CONFIRMATION', // Requiere confirmación del usuario
  BLOCKED = 'BLOCKED'            // No permitir continuar hasta resolver
}

export interface TransitionValidation {
  status: TransitionStatus;
  title: string;
  message: string;
  details?: string[];
  recommendations?: TransitionRecommendation[];
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  canProceed: boolean;
  requiresConfirmation: boolean;
}

export interface TransitionRecommendation {
  id: string;
  type: 'action' | 'alternative' | 'warning';
  title: string;
  description: string;
  action?: () => void;
}

export interface TransitionContext {
  serviceData: ServiceData;
  currentStep: number;
  targetStep: number;
  viabilityAnalysis?: SmartViabilityAnalysis;
}

/**
 * Valida la transición entre pasos y devuelve advertencias contextuales
 */
export function validateStepTransition(
  transitionType: TransitionType,
  context: TransitionContext
): TransitionValidation {
  switch (transitionType) {
    case TransitionType.DIAGNOSIS_TO_DESIRED:
      return validateDiagnosisToDesired(context);
    case TransitionType.DESIRED_TO_FORMULATION:
      return validateDesiredToFormulation(context);
    case TransitionType.FORMULATION_TO_COMPLETION:
      return validateFormulationToCompletion(context);
    default:
      return createDefaultValidation();
  }
}

/**
 * Validación: Diagnóstico → Color Deseado
 */
function validateDiagnosisToDesired(context: TransitionContext): TransitionValidation {
  const { serviceData } = context;
  const issues: string[] = [];
  const recommendations: TransitionRecommendation[] = [];

  // Verificar completitud del diagnóstico
  if (!serviceData.overallTone || !serviceData.overallReflect) {
    issues.push('Diagnóstico de color incompleto');
    recommendations.push({
      id: 'complete_diagnosis',
      type: 'action',
      title: 'Completar Diagnóstico',
      description: 'Especifica el tono y reflejo base del cabello'
    });
  }

  // Verificar análisis por zonas
  const zones = [HairZone.ROOTS, HairZone.MIDS, HairZone.ENDS];
  const missingZones = zones.filter(zone => !serviceData.zoneColorAnalysis?.[zone]?.level);
  
  if (missingZones.length > 0) {
    issues.push(`Análisis incompleto en ${missingZones.length} zona(s)`);
    recommendations.push({
      id: 'complete_zones',
      type: 'action',
      title: 'Completar Análisis por Zonas',
      description: 'Analiza el nivel de color en todas las zonas del cabello'
    });
  }

  // Verificar condición física del cabello
  const hasSevereDamage = zones.some(zone => 
    serviceData.zonePhysicalAnalysis?.[zone]?.damage === 'Alto'
  );

  if (hasSevereDamage) {
    recommendations.push({
      id: 'damage_warning',
      type: 'warning',
      title: 'Cabello Muy Dañado Detectado',
      description: 'Considera opciones de coloración más suaves o tratamiento previo'
    });
  }

  // Determinar estado de la transición
  if (issues.length > 0) {
    return {
      status: TransitionStatus.BLOCKED,
      title: 'Diagnóstico Incompleto',
      message: 'Completa el diagnóstico antes de continuar',
      details: issues,
      recommendations,
      riskLevel: 'medium',
      canProceed: false,
      requiresConfirmation: false
    };
  }

  if (hasSevereDamage) {
    return {
      status: TransitionStatus.WARNING,
      title: 'Cabello Dañado Detectado',
      message: 'El cabello presenta daño severo. Considera opciones más suaves.',
      recommendations,
      riskLevel: 'high',
      canProceed: true,
      requiresConfirmation: false
    };
  }

  return {
    status: TransitionStatus.PROCEED,
    title: 'Diagnóstico Completo',
    message: 'Listo para definir el color deseado',
    riskLevel: 'low',
    canProceed: true,
    requiresConfirmation: false
  };
}

/**
 * Validación: Color Deseado → Formulación
 */
function validateDesiredToFormulation(context: TransitionContext): TransitionValidation {
  const { serviceData } = context;
  const recommendations: TransitionRecommendation[] = [];

  // Verificar que hay resultado de color deseado
  if (!serviceData.desiredAnalysisResult) {
    return {
      status: TransitionStatus.BLOCKED,
      title: 'Color Deseado No Definido',
      message: 'Define el color objetivo antes de continuar',
      riskLevel: 'medium',
      canProceed: false,
      requiresConfirmation: false
    };
  }

  // Analizar viabilidad si no está disponible
  let viabilityAnalysis = context.viabilityAnalysis;
  if (!viabilityAnalysis && serviceData.zoneColorAnalysis) {
    viabilityAnalysis = analyzeSmartViability(
      {
        averageLevel: serviceData.zoneColorAnalysis[HairZone.ROOTS]?.level || 5,
        overallTone: serviceData.overallTone,
        detectedRisks: {
          metallic: false,
          henna: false,
          damaged: Object.values(serviceData.zonePhysicalAnalysis || {})
            .some(zone => zone?.damage === 'Alto')
        }
      },
      serviceData.desiredAnalysisResult,
      serviceData.zoneColorAnalysis
    );
  }

  // Evaluar según estado del semáforo
  if (viabilityAnalysis) {
    switch (viabilityAnalysis.status) {
      case ViabilityStatus.GREEN:
        return {
          status: TransitionStatus.PROCEED,
          title: 'Proceso Directo Viable',
          message: 'Coloración segura en una sesión. Procede con confianza.',
          riskLevel: 'low',
          canProceed: true,
          requiresConfirmation: false
        };

      case ViabilityStatus.AMBER:
        recommendations.push({
          id: 'accept_gradual',
          type: 'action',
          title: 'Aceptar Proceso Gradual',
          description: `${viabilityAnalysis.sessionPlan.totalSessions} sesiones durante ${viabilityAnalysis.sessionPlan.estimatedTimeframe}`
        });

        recommendations.push({
          id: 'view_alternatives',
          type: 'alternative',
          title: 'Ver Alternativas',
          description: 'Explorar opciones más directas'
        });

        return {
          status: TransitionStatus.CONFIRMATION,
          title: 'Proceso Gradual Requerido',
          message: `Este cambio requerirá ${viabilityAnalysis.sessionPlan.totalSessions} sesiones. ¿Continuar con plan gradual?`,
          details: viabilityAnalysis.sessionPlan.sessionDetails.map(session => 
            `Sesión ${session.sessionNumber}: ${session.description}`
          ),
          recommendations,
          riskLevel: 'medium',
          canProceed: true,
          requiresConfirmation: true
        };

      case ViabilityStatus.RED:
        if (viabilityAnalysis.alternatives) {
          recommendations.push(...viabilityAnalysis.alternatives.map(alt => ({
            id: `alt_${alt.targetColor.replace(/\s+/g, '_')}`,
            type: 'alternative' as const,
            title: alt.targetColor,
            description: alt.description
          })));
        }

        return {
          status: TransitionStatus.BLOCKED,
          title: 'Proceso No Recomendado',
          message: 'Riesgo significativo de daño capilar. Considera alternativas más seguras.',
          details: viabilityAnalysis.riskFactors.map(risk => risk.description),
          recommendations,
          riskLevel: 'critical',
          canProceed: false,
          requiresConfirmation: false
        };
    }
  }

  // Fallback si no hay análisis de viabilidad
  return {
    status: TransitionStatus.WARNING,
    title: 'Análisis de Viabilidad Pendiente',
    message: 'Recomendamos analizar la viabilidad antes de proceder',
    riskLevel: 'medium',
    canProceed: true,
    requiresConfirmation: false
  };
}

/**
 * Validación: Formulación → Finalización
 */
function validateFormulationToCompletion(context: TransitionContext): TransitionValidation {
  const { serviceData } = context;
  const issues: string[] = [];
  const recommendations: TransitionRecommendation[] = [];

  // Verificar que hay fórmula
  if (!serviceData.formula) {
    issues.push('Fórmula no generada');
  }

  // Verificar marca y línea seleccionadas
  if (!serviceData.selectedBrand || !serviceData.selectedLine) {
    issues.push('Marca y línea no seleccionadas');
  }

  // Verificar stock si está disponible
  if (serviceData.stockValidation?.checked && !serviceData.stockValidation.hasStock) {
    recommendations.push({
      id: 'check_stock',
      type: 'warning',
      title: 'Productos Faltantes',
      description: `Faltan ${serviceData.stockValidation.missingProducts.length} productos en inventario`
    });
  }

  // Verificar viabilidad de la fórmula
  if (serviceData.viabilityAnalysis?.score === 'risky') {
    recommendations.push({
      id: 'risky_formula',
      type: 'warning',
      title: 'Fórmula de Alto Riesgo',
      description: 'Documenta los riesgos y obtén consentimiento del cliente'
    });
  }

  if (issues.length > 0) {
    return {
      status: TransitionStatus.BLOCKED,
      title: 'Formulación Incompleta',
      message: 'Completa la formulación antes de proceder',
      details: issues,
      recommendations,
      riskLevel: 'medium',
      canProceed: false,
      requiresConfirmation: false
    };
  }

  if (recommendations.length > 0) {
    return {
      status: TransitionStatus.WARNING,
      title: 'Advertencias de Formulación',
      message: 'Revisa las advertencias antes de aplicar',
      recommendations,
      riskLevel: 'medium',
      canProceed: true,
      requiresConfirmation: false
    };
  }

  return {
    status: TransitionStatus.PROCEED,
    title: 'Listo para Aplicar',
    message: 'Formulación completa y validada',
    riskLevel: 'low',
    canProceed: true,
    requiresConfirmation: false
  };
}

/**
 * Validación por defecto
 */
function createDefaultValidation(): TransitionValidation {
  return {
    status: TransitionStatus.PROCEED,
    title: 'Continuar',
    message: 'Listo para el siguiente paso',
    riskLevel: 'low',
    canProceed: true,
    requiresConfirmation: false
  };
}

/**
 * Obtiene el tipo de transición basado en los pasos
 */
export function getTransitionType(currentStep: number, targetStep: number): TransitionType | null {
  if (currentStep === 0 && targetStep === 1) return TransitionType.DIAGNOSIS_TO_DESIRED;
  if (currentStep === 1 && targetStep === 2) return TransitionType.DESIRED_TO_FORMULATION;
  if (currentStep === 2 && targetStep === 3) return TransitionType.FORMULATION_TO_COMPLETION;
  return null;
}
