/**
 * Centralized viability analysis for hair coloring services
 * Uses professional colorimetry principles for consistent calculations
 */

import { ViabilityAnalysis } from '@/types/formulation';
import { HairZone, ZoneColorAnalysis } from '@/types/hair-diagnosis';
import { DesiredColorAnalysisResult } from '@/types/desired-analysis';
import {
  ViabilityStatus,
  AlternativeRecommendation
} from '@/types/alternative-recommendations';

/**
 * NEW: Semáforo Inteligente de Viabilidad
 * Estados claros para guiar el flujo de la aplicación
 */

export interface SmartViabilityAnalysis extends ViabilityAnalysis {
  status: ViabilityStatus;
  sessionPlan: SessionPlan;
  riskFactors: RiskFactor[];
  alternatives?: AlternativeRecommendation[];
  professionalNotes: string;
}

export interface SessionPlan {
  totalSessions: number;
  sessionDetails: SessionDetail[];
  estimatedTimeframe: string; // "2-3 semanas", "1-2 meses"
  totalCost?: number;
}

export interface SessionDetail {
  sessionNumber: number;
  process: ProcessType[];
  description: string;
  estimatedTime: number; // minutes
  waitTime?: number; // days between sessions
  expectedResult: string;
}

export interface RiskFactor {
  type: 'chemical' | 'physical' | 'technical' | 'client';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  mitigation?: string;
}



export interface ColorProcess {
  currentLevel: number;
  desiredLevel: number;
  currentState: 'natural' | 'colored' | 'bleached' | 'mixed';
  hasMetallicSalts?: boolean;
  hasHenna?: boolean;
}

export interface ProcessValidation {
  isViable: boolean;
  requiredProcesses: ProcessType[];
  warnings: string[];
  recommendedDeveloperVolume: number;
  estimatedSessions: number;
}

export enum ProcessType {
  DIRECT_COLOR = 'direct_color',
  BLEACHING = 'bleaching',
  COLOR_REMOVAL = 'color_removal',
  PRE_PIGMENTATION = 'pre_pigmentation',
  NEUTRALIZATION = 'neutralization',
  TONING = 'toning',
}

// Core colorimetry principles
const COLORIMETRY_PRINCIPLES = {
  COLOR_LIFT_LIMIT: {
    natural: 3, // Natural hair can be lifted up to 3 levels with color
    colored: 0, // Colored hair cannot be lifted with color
    bleached: 0, // Bleached hair cannot be lifted with color
    mixed: 0, // Mixed hair cannot be lifted with color
  },
  PRE_PIGMENT_THRESHOLD: 3, // Need pre-pigment when going darker by 3+ levels
  MAX_LIFT_PER_SESSION: 4,
};

/**
 * Validates if a color process is technically viable and determines required steps
 */
function validateColorProcess(process: ColorProcess): ProcessValidation {
  const { currentLevel, desiredLevel, currentState, hasMetallicSalts, hasHenna } = process;
  const levelDifference = desiredLevel - currentLevel;

  const validation: ProcessValidation = {
    isViable: true,
    requiredProcesses: [],
    warnings: [],
    recommendedDeveloperVolume: 20,
    estimatedSessions: 1,
  };

  // Check for incompatible products
  if (hasMetallicSalts) {
    validation.warnings.push(
      'Presencia de sales metálicas detectada. Requiere test de mechón y posible tratamiento de eliminación.'
    );
    validation.isViable = false;
    return validation;
  }

  if (hasHenna) {
    validation.warnings.push(
      'Presencia de henna detectada. La decoloración puede causar reacciones impredecibles.'
    );
    validation.warnings.push('Se recomienda cortar el cabello con henna o esperar a que crezca.');
  }

  // Determine process based on level change and current state
  if (levelDifference > 0) {
    // LIGHTENING PROCESS
    const maxLiftWithColor = COLORIMETRY_PRINCIPLES.COLOR_LIFT_LIMIT[currentState];

    if (levelDifference > maxLiftWithColor) {
      // Cannot achieve with color alone
      if (currentState === 'colored') {
        validation.requiredProcesses.push(ProcessType.COLOR_REMOVAL);
        validation.warnings.push(
          'Color no levanta color. Se requiere decapado previo para eliminar pigmentos artificiales.'
        );
      }

      validation.requiredProcesses.push(ProcessType.BLEACHING);

      // Calculate sessions needed
      const liftsNeeded = levelDifference - maxLiftWithColor;
      validation.estimatedSessions = Math.ceil(
        liftsNeeded / COLORIMETRY_PRINCIPLES.MAX_LIFT_PER_SESSION
      );

      if (validation.estimatedSessions > 1) {
        validation.warnings.push(
          `Se requieren ${validation.estimatedSessions} sesiones para alcanzar el nivel deseado de forma segura.`
        );
      }

      // Set developer volume for bleaching
      validation.recommendedDeveloperVolume = 30;
    } else {
      // Can achieve with color
      validation.requiredProcesses.push(ProcessType.DIRECT_COLOR);

      // Determine developer volume based on lift needed
      if (levelDifference <= 1) {
        validation.recommendedDeveloperVolume = 20;
      } else if (levelDifference <= 2) {
        validation.recommendedDeveloperVolume = 30;
      } else {
        validation.recommendedDeveloperVolume = 40;
      }
    }
  } else if (levelDifference < 0) {
    // DARKENING PROCESS
    const levelsDarker = Math.abs(levelDifference);

    if (levelsDarker >= COLORIMETRY_PRINCIPLES.PRE_PIGMENT_THRESHOLD) {
      validation.requiredProcesses.push(ProcessType.PRE_PIGMENTATION);
      validation.warnings.push(
        `Pre-pigmentación requerida al oscurecer ${levelsDarker} niveles para evitar resultados verdosos.`
      );
    }

    validation.requiredProcesses.push(ProcessType.DIRECT_COLOR);

    // For darkening, only need low volume developer
    validation.recommendedDeveloperVolume = 10;
  } else {
    // SAME LEVEL (tone change)
    validation.requiredProcesses.push(ProcessType.DIRECT_COLOR);

    // Check if it's just a toning process
    if (currentState === 'bleached' || currentLevel >= 9) {
      validation.requiredProcesses = [ProcessType.TONING];
      validation.recommendedDeveloperVolume = 10;
    } else {
      validation.recommendedDeveloperVolume = 20;
    }
  }

  // Add specific warnings for challenging processes
  if (currentLevel <= 3 && desiredLevel >= 9) {
    validation.warnings.push(
      'Proceso de alto riesgo: aclarar cabello muy oscuro a rubio muy claro puede causar daño severo.'
    );
    validation.warnings.push(
      'Se recomienda encarecidamente realizar el proceso en múltiples sesiones con tratamientos intermedios.'
    );
  }

  if (currentState === 'bleached' && levelDifference < -3) {
    validation.warnings.push(
      'Oscurecer cabello decolorado requiere especial atención a la porosidad.'
    );
    validation.warnings.push(
      'Considerar usar un relleno de color o tratamiento de porosidad previo.'
    );
  }

  return validation;
}

/**
 * Analyzes service viability using colorimetry principles
 * This is the centralized function that all components should use
 */
export function analyzeServiceViability(
  analysisResult: {
    level?: number;
    averageLevel?: number;
    averageDepthLevel?: number;
    [key: string]: unknown;
  },
  desiredAnalysisResult: DesiredColorAnalysisResult,
  zoneColorAnalysis: Record<HairZone, Partial<ZoneColorAnalysis>>
): ViabilityAnalysis {
  // Extract current and desired levels
  const currentLevel =
    analysisResult?.level ||
    analysisResult?.averageLevel ||
    analysisResult?.averageDepthLevel ||
    zoneColorAnalysis?.[HairZone.ROOTS]?.level ||
    5;

  const targetLevel =
    parseInt(desiredAnalysisResult?.general?.overallLevel?.split('/')[0] || '7') || 7;

  const levelDifference = Math.abs(targetLevel - currentLevel);

  // Determine current hair state
  const detectedProcess = analysisResult?.detectedChemicalProcess?.toLowerCase() || '';
  const state = analysisResult?.state?.toLowerCase() || '';

  const currentState =
    detectedProcess.includes('color') || state.includes('teñido') || state.includes('colored')
      ? 'colored'
      : detectedProcess.includes('bleach') ||
          state.includes('decolorado') ||
          state.includes('bleached')
        ? 'bleached'
        : 'natural';

  // Create color process for validation
  const colorProcess: ColorProcess = {
    currentLevel: Math.round(currentLevel),
    desiredLevel: Math.round(targetLevel),
    currentState: currentState as 'natural' | 'colored' | 'bleached',
    hasMetallicSalts: analysisResult?.detectedRisks?.metallic || false,
    hasHenna: analysisResult?.detectedRisks?.henna || false,
  };

  // Validate using colorimetry principles
  const validation = validateColorProcess(colorProcess);

  // Analyze hair health
  const rootsPhysical = zoneColorAnalysis?.[HairZone.ROOTS];
  const midsPhysical = zoneColorAnalysis?.[HairZone.MIDS];
  const endsPhysical = zoneColorAnalysis?.[HairZone.ENDS];

  const hasSevereDamage =
    rootsPhysical?.damage === 'Alto' ||
    midsPhysical?.damage === 'Alto' ||
    endsPhysical?.damage === 'Alto';

  const hasModerateDamage =
    rootsPhysical?.damage === 'Medio' ||
    midsPhysical?.damage === 'Medio' ||
    endsPhysical?.damage === 'Medio';

  // Determine viability score
  let score: 'safe' | 'caution' | 'risky' = 'safe';
  let hairHealth: 'good' | 'fair' | 'poor' = 'good';

  if (!validation.isViable || hasSevereDamage || validation.estimatedSessions > 2) {
    score = 'risky';
    hairHealth = 'poor';
  } else if (
    validation.warnings.length > 0 ||
    hasModerateDamage ||
    validation.estimatedSessions > 1
  ) {
    score = 'caution';
    hairHealth = hasModerateDamage ? 'fair' : 'good';
  }

  // Generate additional warnings based on hair condition
  const warnings = [...validation.warnings];
  if (hasSevereDamage) {
    warnings.push('Cabello muy dañado, considerar tratamiento previo');
  }

  // Generate recommendations
  const recommendations: string[] = [];
  if (levelDifference > 2 || validation.requiredProcesses.length > 1) {
    recommendations.push('Realizar prueba de mecha antes del servicio');
  }
  if (hasSevereDamage || hasModerateDamage) {
    recommendations.push('Aplicar tratamiento reconstructor 1-2 semanas antes');
  }
  if (validation.estimatedSessions > 1) {
    recommendations.push('Considerar realizarlo en múltiples sesiones');
  }
  if (validation.requiredProcesses.includes(ProcessType.COLOR_REMOVAL)) {
    recommendations.push('Evaluar resultado del decapado antes de proceder con la decoloración');
  }
  if (validation.requiredProcesses.includes(ProcessType.PRE_PIGMENTATION)) {
    recommendations.push('Aplicar pre-pigmentación para evitar tonos no deseados');
  }

  return {
    score,
    factors: {
      levelDifference,
      hairHealth,
      chemicalHistory: analysisResult?.lastChemicalProcessType
        ? [analysisResult.lastChemicalProcessType]
        : [],
      estimatedSessions: validation.estimatedSessions,
    },
    warnings,
    recommendations,
  };
}

/**
 * NEW: Semáforo Inteligente de Viabilidad
 * Analiza la viabilidad y devuelve estados claros con planes de sesión
 */
export function analyzeSmartViability(
  analysisResult: {
    level?: number;
    averageLevel?: number;
    averageDepthLevel?: number;
    [key: string]: unknown;
  },
  desiredAnalysisResult: DesiredColorAnalysisResult,
  zoneColorAnalysis: Record<HairZone, Partial<ZoneColorAnalysis>>
): SmartViabilityAnalysis {
  // Primero ejecutar el análisis tradicional
  const baseAnalysis = analyzeServiceViability(analysisResult, desiredAnalysisResult, zoneColorAnalysis);

  // Extraer información clave
  const currentLevel = analysisResult?.level || analysisResult?.averageLevel || analysisResult?.averageDepthLevel || 5;
  const targetLevel = parseInt(desiredAnalysisResult?.general?.overallLevel?.split('/')[0] || '7') || 7;
  const levelDifference = targetLevel - currentLevel;

  // Determinar estado del cabello
  const currentState = determineHairState(analysisResult, zoneColorAnalysis);

  // Validar proceso químico
  const validation = validateColorProcess({
    currentLevel,
    desiredLevel: targetLevel,
    currentState,
    hasMetallicSalts: analysisResult?.detectedRisks?.metallic || false,
    hasHenna: analysisResult?.detectedRisks?.henna || false,
  });

  // Analizar factores de riesgo
  const riskFactors = analyzeRiskFactors(analysisResult, zoneColorAnalysis, validation);

  // Determinar estado del semáforo
  const status = determineViabilityStatus(validation, riskFactors, baseAnalysis);

  // Generar plan de sesiones
  const sessionPlan = generateSessionPlan(validation, status, currentLevel, targetLevel);

  // Generar alternativas si es necesario
  const alternatives = status === ViabilityStatus.RED ?
    generateAlternatives(currentLevel, targetLevel, currentState, analysisResult, zoneAnalysis, validation) : undefined;

  // Generar notas profesionales
  const professionalNotes = generateProfessionalNotes(status, validation, riskFactors);

  return {
    ...baseAnalysis,
    status,
    sessionPlan,
    riskFactors,
    alternatives,
    professionalNotes,
  };
}

/**
 * Determina el estado actual del cabello basado en el análisis
 */
function determineHairState(
  analysisResult: any,
  zoneColorAnalysis: Record<HairZone, Partial<ZoneColorAnalysis>>
): 'natural' | 'colored' | 'bleached' | 'mixed' {
  const detectedProcess = analysisResult?.detectedChemicalProcess || analysisResult?.state;

  if (detectedProcess?.toLowerCase().includes('decolorado') ||
      detectedProcess?.toLowerCase().includes('bleached')) {
    return 'bleached';
  }

  if (detectedProcess?.toLowerCase().includes('teñido') ||
      detectedProcess?.toLowerCase().includes('colored') ||
      detectedProcess?.toLowerCase().includes('tinte')) {
    return 'colored';
  }

  // Verificar si hay mezcla de procesos en diferentes zonas
  const rootsState = zoneColorAnalysis?.[HairZone.ROOTS]?.chemicalHistory;
  const midsState = zoneColorAnalysis?.[HairZone.MIDS]?.chemicalHistory;
  const endsState = zoneColorAnalysis?.[HairZone.ENDS]?.chemicalHistory;

  const states = [rootsState, midsState, endsState].filter(Boolean);
  const uniqueStates = [...new Set(states)];

  if (uniqueStates.length > 1) {
    return 'mixed';
  }

  return 'natural';
}

/**
 * Get colorimetry validation for use in Edge Functions
 * Returns the same validation used in the app
 */
export function getColorimetryValidation(
  currentLevel: number,
  desiredLevel: number,
  currentState: 'natural' | 'colored' | 'bleached',
  hasMetallicSalts = false,
  hasHenna = false
): ProcessValidation {
  return validateColorProcess({
    currentLevel,
    desiredLevel,
    currentState,
    hasMetallicSalts,
    hasHenna,
  });
}

/**
 * Analiza factores de riesgo específicos
 */
function analyzeRiskFactors(
  analysisResult: any,
  zoneColorAnalysis: Record<HairZone, Partial<ZoneColorAnalysis>>,
  validation: ProcessValidation
): RiskFactor[] {
  const riskFactors: RiskFactor[] = [];

  // Riesgos químicos
  if (analysisResult?.detectedRisks?.metallic) {
    riskFactors.push({
      type: 'chemical',
      severity: 'critical',
      description: 'Sales metálicas detectadas',
      mitigation: 'Test de mechón obligatorio y posible tratamiento de eliminación'
    });
  }

  if (analysisResult?.detectedRisks?.henna) {
    riskFactors.push({
      type: 'chemical',
      severity: 'high',
      description: 'Henna detectada en el cabello',
      mitigation: 'Evitar procesos de aclaración, considerar solo tonos más oscuros'
    });
  }

  // Riesgos físicos por daño
  const zones = [HairZone.ROOTS, HairZone.MIDS, HairZone.ENDS];
  zones.forEach(zone => {
    const zoneData = zoneColorAnalysis[zone];
    if (zoneData?.damage === 'Alto') {
      riskFactors.push({
        type: 'physical',
        severity: 'high',
        description: `Daño severo detectado en ${zone.toLowerCase()}`,
        mitigation: 'Tratamiento reconstructivo previo recomendado'
      });
    } else if (zoneData?.damage === 'Medio') {
      riskFactors.push({
        type: 'physical',
        severity: 'medium',
        description: `Daño moderado en ${zone.toLowerCase()}`,
        mitigation: 'Usar protectores durante el proceso'
      });
    }
  });

  // Riesgos técnicos
  if (validation.estimatedSessions > 2) {
    riskFactors.push({
      type: 'technical',
      severity: 'medium',
      description: `Proceso complejo requiere ${validation.estimatedSessions} sesiones`,
      mitigation: 'Planificar proceso gradual con descansos entre sesiones'
    });
  }

  if (validation.requiredProcesses.includes(ProcessType.COLOR_REMOVAL)) {
    riskFactors.push({
      type: 'technical',
      severity: 'high',
      description: 'Requiere decapado de color artificial',
      mitigation: 'Usar decapantes suaves y evaluar resultado antes de continuar'
    });
  }

  return riskFactors;
}

/**
 * Determina el estado del semáforo basado en análisis completo
 */
function determineViabilityStatus(
  validation: ProcessValidation,
  riskFactors: RiskFactor[],
  baseAnalysis: ViabilityAnalysis
): ViabilityStatus {
  // ROJO: Factores críticos o múltiples riesgos altos
  const criticalRisks = riskFactors.filter(r => r.severity === 'critical');
  const highRisks = riskFactors.filter(r => r.severity === 'high');

  if (criticalRisks.length > 0 || !validation.isViable) {
    return ViabilityStatus.RED;
  }

  if (highRisks.length >= 2 || validation.estimatedSessions > 3) {
    return ViabilityStatus.RED;
  }

  // ÁMBAR: Proceso complejo pero viable
  if (validation.estimatedSessions > 1 ||
      highRisks.length > 0 ||
      riskFactors.filter(r => r.severity === 'medium').length >= 2 ||
      baseAnalysis.score === 'caution') {
    return ViabilityStatus.AMBER;
  }

  // VERDE: Proceso directo y seguro
  return ViabilityStatus.GREEN;
}

/**
 * Genera plan detallado de sesiones
 */
function generateSessionPlan(
  validation: ProcessValidation,
  status: ViabilityStatus,
  currentLevel: number,
  targetLevel: number
): SessionPlan {
  const sessionDetails: SessionDetail[] = [];
  let totalSessions = validation.estimatedSessions;

  if (status === ViabilityStatus.GREEN) {
    // Sesión única
    sessionDetails.push({
      sessionNumber: 1,
      process: validation.requiredProcesses.length > 0 ? validation.requiredProcesses : [ProcessType.DIRECT_COLOR],
      description: 'Coloración directa en una sesión',
      estimatedTime: 120,
      expectedResult: `Nivel ${targetLevel} alcanzado`
    });
  } else if (status === ViabilityStatus.AMBER) {
    // Múltiples sesiones planificadas
    if (validation.requiredProcesses.includes(ProcessType.COLOR_REMOVAL)) {
      sessionDetails.push({
        sessionNumber: 1,
        process: [ProcessType.COLOR_REMOVAL],
        description: 'Decapado suave para eliminar color artificial',
        estimatedTime: 90,
        waitTime: 7,
        expectedResult: 'Color artificial eliminado, cabello preparado'
      });
      totalSessions++;
    }

    if (validation.requiredProcesses.includes(ProcessType.BLEACHING)) {
      const liftsNeeded = Math.max(1, targetLevel - currentLevel - 2);
      const bleachingSessions = Math.ceil(liftsNeeded / 3);

      for (let i = 0; i < bleachingSessions; i++) {
        sessionDetails.push({
          sessionNumber: sessionDetails.length + 1,
          process: [ProcessType.BLEACHING],
          description: `Aclaración gradual - Sesión ${i + 1}`,
          estimatedTime: 150,
          waitTime: i < bleachingSessions - 1 ? 14 : 7,
          expectedResult: `Nivel ${currentLevel + (i + 1) * 3} alcanzado`
        });
      }
    }

    // Sesión final de color
    sessionDetails.push({
      sessionNumber: sessionDetails.length + 1,
      process: [ProcessType.DIRECT_COLOR, ProcessType.TONING],
      description: 'Aplicación de color final y tonalización',
      estimatedTime: 120,
      expectedResult: `Color final nivel ${targetLevel} logrado`
    });
  }

  const timeframeWeeks = Math.ceil(totalSessions * 1.5);
  const estimatedTimeframe = timeframeWeeks <= 4 ?
    `${timeframeWeeks} semanas` :
    `${Math.ceil(timeframeWeeks / 4)} meses`;

  return {
    totalSessions: sessionDetails.length,
    sessionDetails,
    estimatedTimeframe
  };
}

/**
 * Genera alternativas automáticas usando el sistema básico
 */
function generateAlternatives(
  currentLevel: number,
  targetLevel: number,
  currentState: 'natural' | 'colored' | 'bleached' | 'mixed',
  analysisResult?: DesiredColorAnalysisResult,
  zoneAnalysis?: Record<HairZone, Partial<ZoneColorAnalysis>>,
  validation?: ProcessValidation
): AlternativeRecommendation[] {
  // Usar el sistema básico para evitar dependencias circulares
  return generateBasicAlternatives(currentLevel, targetLevel, validation);
}

/**
 * Genera alternativas básicas como fallback
 */
function generateBasicAlternatives(
  currentLevel: number,
  targetLevel: number,
  validation?: ProcessValidation
): AlternativeRecommendation[] {
  const alternatives: AlternativeRecommendation[] = [];

  // Si quiere aclarar mucho, sugerir tonos más cercanos
  if (targetLevel > currentLevel + 2) {
    const saferLevel = currentLevel + 2;
    alternatives.push({
      targetColor: `Nivel ${saferLevel}`,
      reason: 'Aclaración gradual más segura para la estructura capilar',
      viabilityImprovement: ViabilityStatus.AMBER,
      description: `En lugar de nivel ${targetLevel}, considera nivel ${saferLevel} como primer paso`
    });

    // Sugerir mechas como alternativa
    alternatives.push({
      targetColor: 'Mechas o balayage',
      reason: 'Efecto de aclaración sin comprometer todo el cabello',
      viabilityImprovement: ViabilityStatus.GREEN,
      description: 'Técnica de mechas para lograr luminosidad sin daño generalizado'
    });
  }

  // Para cabello muy dañado, sugerir tratamientos
  if (validation && validation.warnings.some(w => w.includes('dañado'))) {
    alternatives.push({
      targetColor: 'Tratamiento reconstructivo + color suave',
      reason: 'Priorizar salud capilar antes del cambio de color',
      viabilityImprovement: ViabilityStatus.AMBER,
      description: 'Plan de recuperación capilar seguido de coloración suave'
    });
  }

  return alternatives;
}

/**
 * Determina la condición del cabello basada en la validación
 */
function determineHairCondition(validation: ProcessValidation): 'excellent' | 'good' | 'fair' | 'poor' | 'damaged' {
  if (validation.warnings.some(w => w.includes('muy dañado') || w.includes('severo'))) {
    return 'damaged';
  }
  if (validation.warnings.some(w => w.includes('dañado') || w.includes('poroso'))) {
    return 'poor';
  }
  if (validation.warnings.length > 2) {
    return 'fair';
  }
  if (validation.warnings.length > 0) {
    return 'good';
  }
  return 'excellent';
}

/**
 * Genera notas profesionales específicas para el estado
 */
function generateProfessionalNotes(
  status: ViabilityStatus,
  validation: ProcessValidation,
  riskFactors: RiskFactor[]
): string {
  let notes = '';

  switch (status) {
    case ViabilityStatus.GREEN:
      notes = '✅ PROCESO DIRECTO: Cabello en condiciones óptimas para coloración en una sesión. ';
      notes += 'Procede con confianza siguiendo protocolos estándar de la marca seleccionada.';
      break;

    case ViabilityStatus.AMBER:
      notes = '⚠️ PROCESO GRADUAL: Requiere planificación de múltiples sesiones para resultado óptimo. ';
      notes += `Se recomienda ${validation.estimatedSessions} sesiones con descansos de 1-2 semanas entre cada una. `;
      notes += 'Evaluar resultado en cada sesión antes de proceder.';
      break;

    case ViabilityStatus.RED:
      notes = '🚫 PROCESO NO RECOMENDADO: Riesgo significativo de daño capilar. ';
      notes += 'Considera alternativas más seguras o tratamiento reconstructivo previo. ';
      notes += 'Si el cliente insiste, documenta riesgos y obtén consentimiento informado.';
      break;
  }

  // Agregar notas específicas de riesgos críticos
  const criticalRisks = riskFactors.filter(r => r.severity === 'critical');
  if (criticalRisks.length > 0) {
    notes += '\n\n🔴 RIESGOS CRÍTICOS:\n';
    criticalRisks.forEach(risk => {
      notes += `• ${risk.description}: ${risk.mitigation}\n`;
    });
  }

  // Agregar notas de procesos especiales
  if (validation.requiredProcesses.includes(ProcessType.COLOR_REMOVAL)) {
    notes += '\n💡 DECAPADO REQUERIDO: Usar productos suaves y evaluar porosidad resultante.';
  }

  if (validation.requiredProcesses.includes(ProcessType.PRE_PIGMENTATION)) {
    notes += '\n🎨 PRE-PIGMENTACIÓN: Esencial para evitar resultado cenizo o verdoso.';
  }

  return notes.trim();
}
